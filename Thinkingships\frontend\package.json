{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "host": "vite --host"}, "dependencies": {"@reduxjs/toolkit": "^2.8.2", "@tailwindcss/vite": "^4.1.10", "axios": "^1.10.0", "daisyui": "^5.0.43", "framer-motion": "^12.23.6", "js-cookie": "^3.0.5", "lucide-react": "^0.515.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.6.2", "react-select": "^5.10.1", "socket.io-client": "^4.8.1", "tailwind-scrollbar": "^4.0.2"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.10", "vite": "^6.3.5", "vite-plugin-svgr": "^4.3.0"}}