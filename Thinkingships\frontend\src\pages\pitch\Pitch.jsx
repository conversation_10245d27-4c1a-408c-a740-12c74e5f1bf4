import { useState } from 'react';

const Pitch = () => {
  const [activeTab, setActiveTab] = useState('Open');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [filters, setFilters] = useState({
    contentType: 'All',
    genre: 'All',
    language: 'All',
    payout: 'All'
  });
  const [formData, setFormData] = useState({
    title: '',
    brief: '',
    contentType: '',
    genre: '',
    deadline: '',
    wordCount: '',
    language: '',
    tags: '',
    guidelines: '',
    requireFirstRights: false,
    showSubmissionCount: false,
    autoCloseDeadline: false
  });

  const openPitchRequests = [
    {
      id: 1,
      title: 'Pitch Request By Inkspire Magazine',
      publication: 'Voices From The City',
      genre: 'Urban Life',
      type: 'Blog',
      payout: '₹500',
      submissions: 500,
      status: 'Open',
      description: 'Share A Compelling Article About Life In An Indian Metro City — The Chaos, The Quiet Moments, The Everyday Characters, And The Ever-Changing Pace.',
      avatar: 'https://randomuser.me/api/portraits/women/44.jpg'
    },
    {
      id: 2,
      title: 'Pitch Request By Inkspire Magazine',
      publication: 'Echoes Of The Forgotten',
      genre: 'Romance',
      type: 'Story',
      payout: '₹500',
      submissions: 500,
      status: 'Open',
      description: 'A Haunting Narrative About People, Places, Or Memories That Time Has Left Behind. We\'re Looking For Stories That Blend Nostalgia With Quiet Revelation.',
      avatar: 'https://randomuser.me/api/portraits/men/32.jpg'
    },
    {
      id: 3,
      title: 'Pitch Request By Literary Digest',
      publication: 'Modern Myths',
      genre: 'Fantasy',
      type: 'Story',
      payout: '₹750',
      submissions: 320,
      status: 'Open',
      description: 'Contemporary retellings of ancient myths and folklore. We want fresh perspectives on timeless stories that resonate with modern audiences.',
      avatar: 'https://randomuser.me/api/portraits/women/68.jpg'
    },
    {
      id: 4,
      title: 'Pitch Request By Creative Writers Hub',
      publication: 'Tech Tomorrow',
      genre: 'Science Fiction',
      type: 'Article',
      payout: '₹600',
      submissions: 150,
      status: 'Open',
      description: 'Explore the intersection of technology and human emotion. How will AI, robotics, and digital transformation shape our relationships and society?',
      avatar: 'https://randomuser.me/api/portraits/men/75.jpg'
    },
    {
      id: 5,
      title: 'Pitch Request By Nature\'s Voice',
      publication: 'Green Horizons',
      genre: 'Environmental',
      type: 'Blog',
      payout: '₹400',
      submissions: 280,
      status: 'Open',
      description: 'Stories about environmental conservation, sustainable living, and the beauty of nature. Share your experiences and insights about protecting our planet.',
      avatar: 'https://randomuser.me/api/portraits/women/89.jpg'
    }
  ];

  const closedPitchRequests = [
    {
      id: 6,
      title: 'Pitch Request By Writers Weekly',
      publication: 'Midnight Stories',
      genre: 'Horror',
      type: 'Story',
      payout: '₹800',
      submissions: 750,
      status: 'Closed',
      description: 'Dark tales that explore the supernatural and psychological horror. Stories that keep readers awake at night with their haunting narratives.',
      avatar: 'https://randomuser.me/api/portraits/women/25.jpg'
    },
    {
      id: 7,
      title: 'Pitch Request By Travel Tales',
      publication: 'Wanderlust Chronicles',
      genre: 'Travel',
      type: 'Article',
      payout: '₹550',
      submissions: 420,
      status: 'Closed',
      description: 'Share your most memorable travel experiences, hidden gems, and cultural discoveries from around the world.',
      avatar: 'https://randomuser.me/api/portraits/men/58.jpg'
    },
    {
      id: 8,
      title: 'Pitch Request By Food & Culture',
      publication: 'Culinary Heritage',
      genre: 'Food',
      type: 'Blog',
      payout: '₹450',
      submissions: 380,
      status: 'Closed',
      description: 'Explore traditional recipes, food culture, and the stories behind regional cuisines that define our heritage.',
      avatar: 'https://randomuser.me/api/portraits/women/72.jpg'
    },
    {
      id: 9,
      title: 'Pitch Request By Tech Innovators',
      publication: 'Future Forward',
      genre: 'Technology',
      type: 'Article',
      payout: '₹700',
      submissions: 290,
      status: 'Closed',
      description: 'Insights into emerging technologies, startup stories, and innovations that are shaping the future of business.',
      avatar: 'https://randomuser.me/api/portraits/men/41.jpg'
    }
  ];

  const handleFilterChange = (filterType, value) => {
    setFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const handleFormChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCreateNew = () => {
    setShowCreateForm(true);
  };

  const handleCloseForm = () => {
    setShowCreateForm(false);
    setFormData({
      title: '',
      brief: '',
      contentType: '',
      genre: '',
      deadline: '',
      wordCount: '',
      language: '',
      tags: '',
      guidelines: '',
      requireFirstRights: false,
      showSubmissionCount: false,
      autoCloseDeadline: false
    });
  };

  const handleSubmitForm = (e) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
    // Handle form submission here
    handleCloseForm();
  };

  const currentPitches = activeTab === 'Open' ? openPitchRequests : closedPitchRequests;

  const filteredPitches = currentPitches.filter(pitch => {
    if (filters.contentType !== 'All' && pitch.type !== filters.contentType) return false;
    if (filters.genre !== 'All' && pitch.genre !== filters.genre) return false;
    if (filters.payout !== 'All' && pitch.payout !== filters.payout) return false;
    return true;
  });

  return (
    <div className="p-6 bg-gradient-to-br from-slate-50 to-slate-200 min-h-screen relative overflow-y-auto overflow-x-hidden opacity-0" style={{ animation: 'fadeInUp 0.6s ease-out forwards' }}>
      {/* Header, Tabs, and Filters - Hide when create form is shown */}
      {!showCreateForm && (
        <>
          {/* Header */}
          <div className="flex justify-between items-center mb-8 relative z-10 opacity-0 -translate-y-4" style={{ animation: 'slideInUp 0.6s ease-out 0.1s forwards' }}>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-500 to-blue-700 bg-clip-text text-transparent relative">
              Pitch Hub
              <div className="absolute -bottom-2 left-0 w-15 h-1 bg-gradient-to-r from-blue-500 to-blue-700 rounded-full"></div>
            </h1>
            <button
              className="bg-gradient-to-r from-blue-500 to-blue-700 text-white px-6 py-3 rounded-xl font-semibold transition-all duration-300 hover:-translate-y-1 hover:shadow-lg hover:shadow-blue-500/40 relative overflow-hidden group"
              onClick={handleCreateNew}
            >
              <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-500"></span>
              Create New
            </button>
          </div>

          {/* Tabs */}
          <div className="flex gap-2 mb-6 opacity-0 -translate-x-4" style={{ animation: 'slideInUp 0.6s ease-out 0.2s forwards' }}>
            <button
              className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 relative overflow-hidden group ${
                activeTab === 'Open'
                  ? 'bg-blue-500 text-white shadow-lg transform -translate-y-1'
                  : 'bg-white/70 text-gray-600 hover:bg-white/90 hover:text-gray-800'
              }`}
              onClick={() => setActiveTab('Open')}
            >
              <span className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-500/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-500"></span>
              Open
            </button>
            <button
              className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 relative overflow-hidden group ${
                activeTab === 'Closed'
                  ? 'bg-blue-500 text-white shadow-lg transform -translate-y-1'
                  : 'bg-white/70 text-gray-600 hover:bg-white/90 hover:text-gray-800'
              }`}
              onClick={() => setActiveTab('Closed')}
            >
              <span className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-500/10 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-500"></span>
              Closed
            </button>
          </div>

          {/* Filters */}
          <div className="flex flex-wrap gap-4 mb-8 opacity-0 translate-x-4" style={{ animation: 'slideInUp 0.6s ease-out 0.3s forwards' }}>
        <select
          value={filters.contentType}
          onChange={(e) => handleFilterChange('contentType', e.target.value)}
          className="px-4 py-3 bg-white/90 backdrop-blur-sm border-2 border-blue-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 hover:border-blue-300 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-blue-100 min-w-36"
        >
          <option value="All">Content Type</option>
          <option value="Blog">Blog</option>
          <option value="Story">Story</option>
          <option value="Article">Article</option>
        </select>

        <select
          value={filters.genre}
          onChange={(e) => handleFilterChange('genre', e.target.value)}
          className="px-4 py-3 bg-white/90 backdrop-blur-sm border-2 border-blue-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 hover:border-blue-300 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-blue-100 min-w-36"
        >
          <option value="All">Genre</option>
          <option value="Romance">Romance</option>
          <option value="Fantasy">Fantasy</option>
          <option value="Urban Life">Urban Life</option>
          <option value="Science Fiction">Science Fiction</option>
          <option value="Environmental">Environmental</option>
        </select>

        <select
          value={filters.language}
          onChange={(e) => handleFilterChange('language', e.target.value)}
          className="px-4 py-3 bg-white/90 backdrop-blur-sm border-2 border-blue-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 hover:border-blue-300 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-blue-100 min-w-36"
        >
          <option value="All">Language</option>
          <option value="English">English</option>
          <option value="Hindi">Hindi</option>
          <option value="Bengali">Bengali</option>
        </select>

        <select
          value={filters.payout}
          onChange={(e) => handleFilterChange('payout', e.target.value)}
          className="px-4 py-3 bg-white/90 backdrop-blur-sm border-2 border-blue-100 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 hover:border-blue-300 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-blue-100 min-w-36"
        >
          <option value="All">Payout</option>
          <option value="₹400">₹400</option>
          <option value="₹500">₹500</option>
          <option value="₹600">₹600</option>
          <option value="₹750">₹750</option>
        </select>
      </div>
        </>
      )}

      {/* Pitch List or Create Form */}
      {!showCreateForm ? (
        <div className="flex flex-col gap-6 opacity-0" style={{ animation: 'fadeInUp 0.3s ease-out forwards' }} key={activeTab}>
          {filteredPitches.map((pitch, index) => (
          <div
            key={pitch.id}
            className={`bg-white/95 backdrop-blur-xl rounded-2xl p-6 border transition-all duration-300 relative overflow-hidden opacity-0 translate-y-8 hover:scale-105 group ${
              pitch.status.toLowerCase() === 'closed'
                ? 'border-red-100 shadow-red-100 hover:border-red-200 hover:shadow-red-200'
                : 'border-blue-100 shadow-blue-100 hover:border-blue-200 hover:shadow-blue-200'
            }`}
            style={{
              animationDelay: `${index * 0.1}s`,
              animation: 'slideInUp 0.6s ease-out forwards',
              boxShadow: '0 8px 32px rgba(59, 130, 246, 0.08)'
            }}
          >
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-500/5 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-800"></div>
            <div className="flex justify-between items-start mb-5 relative z-10">
              <div className="flex-1">
                <h3 className="text-xl font-semibold text-gray-800 mb-3 leading-tight">{pitch.title}</h3>
                <div className="flex items-center gap-3">
                  <img
                    src={pitch.avatar}
                    alt={pitch.publication}
                    className="w-10 h-10 rounded-full border-2 border-blue-200 transition-all duration-300 hover:scale-110 hover:border-blue-500"
                  />
                  <span className="font-semibold text-gray-700">{pitch.publication}</span>
                </div>
              </div>
              <span className={`px-3 py-1.5 rounded-full text-sm font-semibold uppercase tracking-wide ${
                pitch.status.toLowerCase() === 'open'
                  ? 'bg-gradient-to-r from-green-500 to-green-600 text-white shadow-lg shadow-green-500/30'
                  : 'bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg shadow-red-500/30'
              }`}>
                {pitch.status}
              </span>
            </div>

            <div className="mb-5 relative z-10">
              <div className="flex gap-8 mb-3">
                <div className="flex gap-2">
                  <span className="font-semibold text-gray-600 min-w-20">Genre:</span>
                  <span className="text-gray-800 font-medium">{pitch.genre}</span>
                </div>
                <div className="flex gap-2">
                  <span className="font-semibold text-gray-600 min-w-20">Type:</span>
                  <span className="text-gray-800 font-medium">{pitch.type}</span>
                </div>
              </div>
              <div className="flex gap-8">
                <div className="flex gap-2">
                  <span className="font-semibold text-gray-600 min-w-20">Payout:</span>
                  <span className="text-gray-800 font-medium">{pitch.payout}</span>
                </div>
                <div className="flex gap-2">
                  <span className="font-semibold text-gray-600 min-w-20">Submissions:</span>
                  <span className="text-gray-800 font-medium">{pitch.submissions}</span>
                </div>
              </div>
            </div>

            <div className="mb-6 relative z-10">
              <p className="text-gray-700 leading-relaxed text-sm">{pitch.description}</p>
            </div>

            <div className="flex justify-end relative z-10">
              <button className="bg-gradient-to-r from-blue-500 to-blue-700 text-white px-5 py-2.5 rounded-lg font-semibold transition-all duration-300 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden group">
                <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-500"></span>
                View Details
              </button>
            </div>
          </div>
          ))}
        </div>
      ) : (
        /* Create New Pitch Form */
        <div className="relative z-10 opacity-0" style={{ animation: 'fadeInUp 0.5s ease-out forwards' }}>
          <div className="bg-white/98 backdrop-blur-xl rounded-3xl p-8 w-full shadow-2xl shadow-blue-500/20 border border-blue-100 relative mb-6">
            <div className="flex justify-between items-center mb-6 pb-5 border-b-2 border-blue-100 relative">
              <h2 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
                Create Submission Guidelines
              </h2>
              <button
                className="bg-red-50 hover:bg-red-100 border-none rounded-xl w-11 h-11 flex items-center justify-center cursor-pointer transition-all duration-300 text-red-500 hover:text-red-600 hover:-translate-y-1 hover:shadow-lg hover:shadow-red-200"
                onClick={handleCloseForm}
              >
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
              <div className="absolute bottom-0 left-0 w-20 h-0.5 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full"></div>
            </div>

            <div className="mb-8 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-l-4 border-blue-500 rounded-xl">
              <p className="text-gray-700 leading-relaxed">
                Set up your submission requirements to invite Authors to share their best work. Define what you're looking for and the qualifications Authors need to meet.
              </p>
            </div>

            <form className="flex flex-col gap-6" onSubmit={handleSubmitForm}>
              <div className="flex flex-col gap-2">
                <label className="font-semibold text-gray-800 text-base">Title:</label>
                <input
                  type="text"
                  className="px-4 py-3 border-2 border-blue-100 rounded-xl text-base transition-all duration-300 bg-white/90 backdrop-blur-sm shadow-sm hover:border-blue-300 hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 focus:bg-white focus:-translate-y-0.5"
                  placeholder="Enter title"
                  value={formData.title}
                  onChange={(e) => handleFormChange('title', e.target.value)}
                  required
                />
              </div>

              <div className="flex flex-col gap-2">
                <label className="font-semibold text-gray-800 text-base">Brief:</label>
                <input
                  type="text"
                  className="px-4 py-3 border-2 border-blue-100 rounded-xl text-base transition-all duration-300 bg-white/90 backdrop-blur-sm shadow-sm hover:border-blue-300 hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 focus:bg-white focus:-translate-y-0.5"
                  placeholder="Enter brief description"
                  value={formData.brief}
                  onChange={(e) => handleFormChange('brief', e.target.value)}
                  required
                />
              </div>

              <div className="flex gap-5">
                <div className="flex-1 flex flex-col gap-2">
                  <label className="font-semibold text-gray-800 text-base">Content Type</label>
                  <select
                    className="px-4 py-3 border-2 border-blue-100 rounded-xl text-base transition-all duration-300 bg-white/90 backdrop-blur-sm shadow-sm hover:border-blue-300 hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 focus:bg-white focus:-translate-y-0.5"
                    value={formData.contentType}
                    onChange={(e) => handleFormChange('contentType', e.target.value)}
                    required
                  >
                    <option value="">select</option>
                    <option value="Blog">Blog</option>
                    <option value="Story">Story</option>
                    <option value="Article">Article</option>
                    <option value="Poetry">Poetry</option>
                  </select>
                </div>
                <div className="flex-1 flex flex-col gap-2">
                  <label className="font-semibold text-gray-800 text-base">Genre</label>
                  <select
                    className="px-4 py-3 border-2 border-blue-100 rounded-xl text-base transition-all duration-300 bg-white/90 backdrop-blur-sm shadow-sm hover:border-blue-300 hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 focus:bg-white focus:-translate-y-0.5"
                    value={formData.genre}
                    onChange={(e) => handleFormChange('genre', e.target.value)}
                    required
                  >
                    <option value="">select</option>
                    <option value="Romance">Romance</option>
                    <option value="Fantasy">Fantasy</option>
                    <option value="Mystery">Mystery</option>
                    <option value="Science Fiction">Science Fiction</option>
                    <option value="Horror">Horror</option>
                    <option value="Travel">Travel</option>
                    <option value="Food">Food</option>
                    <option value="Technology">Technology</option>
                  </select>
                </div>
              </div>

              <div className="flex gap-5">
                <div className="flex-1 flex flex-col gap-2">
                  <label className="font-semibold text-gray-800 text-base">Deadline</label>
                  <input
                    type="date"
                    className="px-4 py-3 border-2 border-blue-100 rounded-xl text-base transition-all duration-300 bg-white/90 backdrop-blur-sm shadow-sm hover:border-blue-300 hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 focus:bg-white focus:-translate-y-0.5"
                    value={formData.deadline}
                    onChange={(e) => handleFormChange('deadline', e.target.value)}
                    required
                  />
                </div>
                <div className="flex-1 flex flex-col gap-2">
                  <label className="font-semibold text-gray-800 text-base">Word Count</label>
                  <input
                    type="number"
                    className="px-4 py-3 border-2 border-blue-100 rounded-xl text-base transition-all duration-300 bg-white/90 backdrop-blur-sm shadow-sm hover:border-blue-300 hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 focus:bg-white focus:-translate-y-0.5"
                    placeholder="Enter word count"
                    value={formData.wordCount}
                    onChange={(e) => handleFormChange('wordCount', e.target.value)}
                    required
                  />
                </div>
              </div>

              <div className="flex gap-5">
                <div className="flex-1 flex flex-col gap-2">
                  <label className="font-semibold text-gray-800 text-base">Language</label>
                  <select
                    className="px-4 py-3 border-2 border-blue-100 rounded-xl text-base transition-all duration-300 bg-white/90 backdrop-blur-sm shadow-sm hover:border-blue-300 hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 focus:bg-white focus:-translate-y-0.5"
                    value={formData.language}
                    onChange={(e) => handleFormChange('language', e.target.value)}
                    required
                  >
                    <option value="">select</option>
                    <option value="English">English</option>
                    <option value="Hindi">Hindi</option>
                    <option value="Spanish">Spanish</option>
                    <option value="French">French</option>
                  </select>
                </div>
                <div className="flex-1 flex flex-col gap-2">
                  <label className="font-semibold text-gray-800 text-base">Tags</label>
                  <input
                    type="text"
                    className="px-4 py-3 border-2 border-blue-100 rounded-xl text-base transition-all duration-300 bg-white/90 backdrop-blur-sm shadow-sm hover:border-blue-300 hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 focus:bg-white focus:-translate-y-0.5"
                    placeholder="Enter tags"
                    value={formData.tags}
                    onChange={(e) => handleFormChange('tags', e.target.value)}
                  />
                </div>
              </div>

              <div className="flex flex-col gap-2">
                <label className="font-semibold text-gray-800 text-base">Submission Guidelines</label>
                <div className="text-sm text-gray-600 mb-2">Describe the specific qualities, themes, or styles you expect in submitted drafts</div>
                <textarea
                  className="px-4 py-3 border-2 border-blue-100 rounded-xl text-base transition-all duration-300 bg-white/90 backdrop-blur-sm shadow-sm hover:border-blue-300 hover:-translate-y-0.5 hover:shadow-md focus:outline-none focus:border-blue-500 focus:ring-4 focus:ring-blue-100 focus:bg-white focus:-translate-y-0.5 resize-vertical min-h-36 leading-relaxed"
                  placeholder="Outline what you're looking for in a submission — tone, themes, structure, or any dos and don'ts."
                  value={formData.guidelines}
                  onChange={(e) => handleFormChange('guidelines', e.target.value)}
                  rows="4"
                  required
                />
                <div className="mt-3 p-3 bg-blue-50 border-l-3 border-blue-500 rounded-lg">
                  <em className="text-sm text-gray-700">Tip: Clearly define your content requirements, themes or guidelines to help authors submit work that aligns with your publication's needs.</em>
                </div>
              </div>

              {/* Checkbox Options */}
              <div className="flex flex-col gap-4 mt-2">
                <label className="flex items-center gap-3 cursor-pointer p-3 rounded-xl transition-all duration-300 bg-blue-50/50 border border-blue-100 hover:bg-blue-50 hover:border-blue-200 hover:-translate-y-0.5">
                  <input
                    type="checkbox"
                    className="w-5 h-5 accent-blue-500 cursor-pointer"
                    checked={formData.requireFirstRights}
                    onChange={(e) => handleFormChange('requireFirstRights', e.target.checked)}
                  />
                  <span className="text-base text-gray-800 font-medium">Require first Publication Rights</span>
                </label>

                <label className="flex items-center gap-3 cursor-pointer p-3 rounded-xl transition-all duration-300 bg-blue-50/50 border border-blue-100 hover:bg-blue-50 hover:border-blue-200 hover:-translate-y-0.5">
                  <input
                    type="checkbox"
                    className="w-5 h-5 accent-blue-500 cursor-pointer"
                    checked={formData.showSubmissionCount}
                    onChange={(e) => handleFormChange('showSubmissionCount', e.target.checked)}
                  />
                  <span className="text-base text-gray-800 font-medium">Show number of Submissions</span>
                </label>

                <label className="flex items-center gap-3 cursor-pointer p-3 rounded-xl transition-all duration-300 bg-blue-50/50 border border-blue-100 hover:bg-blue-50 hover:border-blue-200 hover:-translate-y-0.5">
                  <input
                    type="checkbox"
                    className="w-5 h-5 accent-blue-500 cursor-pointer"
                    checked={formData.autoCloseDeadline}
                    onChange={(e) => handleFormChange('autoCloseDeadline', e.target.checked)}
                  />
                  <span className="text-base text-gray-800 font-medium">Auto-Close after Deadline</span>
                </label>
              </div>

              {/* Make Guidelines Live Section */}
              <div className="mt-8 mb-6 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-2xl text-center">
                <h3 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-3">
                  Make Your Guidelines Live
                </h3>
                <p className="text-base text-gray-700 leading-relaxed">
                  Everything's ready! Publish your submission guidelines to start receiving high-quality drafts from talented writers.
                </p>
              </div>

              <div className="flex gap-4 justify-center mt-8 pt-6 border-t border-blue-100">
                <button
                  type="button"
                  className="px-6 py-3 border-2 border-gray-300 bg-transparent text-gray-700 rounded-xl font-semibold cursor-pointer transition-all duration-300 hover:border-gray-500 hover:text-gray-800 hover:-translate-y-0.5"
                >
                  Save As Draft
                </button>
                <button
                  type="button"
                  className="px-6 py-3 border-2 border-blue-500 bg-transparent text-blue-500 rounded-xl font-semibold cursor-pointer transition-all duration-300 hover:bg-blue-50 hover:-translate-y-0.5"
                >
                  Preview
                </button>
                <button
                  type="submit"
                  className="px-8 py-3 bg-gradient-to-r from-blue-500 to-blue-700 text-white border-none rounded-xl font-semibold cursor-pointer transition-all duration-300 hover:-translate-y-1 hover:shadow-xl hover:shadow-blue-500/40 relative overflow-hidden group"
                >
                  <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-500"></span>
                  Set Live
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default Pitch;
