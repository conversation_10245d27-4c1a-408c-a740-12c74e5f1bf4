/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: '#4A99F8',         // for buttons, highlights
        'primary-dark': '#0A06F4',  // for hover states
        secondary: '#76D6FC',       // for secondary buttons
        surface: '#F8F9FA',         // background
        'accent-pink': '#D99FE9',   // for tags, badges
        'accent-purple': '#CDAEEC', // for card/post backgrounds
        info: '#48CAE4',            // for info chips
      },
      animation: {
        'slide-in-left': 'slideInLeft 0.6s ease-out',
        'slide-in-right': 'slideInRight 0.6s ease-out 0.3s both',
        'fade-in-down': 'fadeInDown 0.8s ease-out 0.2s both',
        'fade-in-up': 'fadeInUp 0.8s ease-out 0.7s both',
      },
      keyframes: {
        slideInLeft: {
          'from': {
            transform: 'translateX(-100%)',
            opacity: '0',
          },
          'to': {
            transform: 'translateX(0)',
            opacity: '1',
          },
        },
        slideInRight: {
          'from': {
            transform: 'translateX(100%)',
            opacity: '0',
          },
          'to': {
            transform: 'translateX(0)',
            opacity: '1',
          },
        },
        fadeInDown: {
          'from': {
            transform: 'translateY(-20px)',
            opacity: '0',
          },
          'to': {
            transform: 'translateY(0)',
            opacity: '1',
          },
        },
        fadeInUp: {
          'from': {
            transform: 'translateY(20px)',
            opacity: '0',
          },
          'to': {
            transform: 'translateY(0)',
            opacity: '1',
          },
        },
      },
    },
  },
  plugins: ["@tailwindcss/typography", "daisyui", require('tailwind-scrollbar')],
}
