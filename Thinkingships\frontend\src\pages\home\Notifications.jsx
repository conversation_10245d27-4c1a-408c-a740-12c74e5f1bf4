
import './Notifications.css';

const notificationsByDay = {
  friday: [
    {
      id: 1,
      name: '<PERSON><PERSON><PERSON>',
      avatar: 'https://randomuser.me/api/portraits/women/44.jpg',
      message: 'Replied To Your Comment On Their Skrivee. Continue The Discussion!',
      time: '08:00 PM',
      actions: null
    },
    {
      id: 2,
      name: '<PERSON><PERSON><PERSON><PERSON>',
      avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
      message: 'Shared Their Thoughts On Your Skrivee! Check It Out!',
      time: '11:30 AM',
      actions: null
    },
    {
      id: 3,
      name: '<PERSON><PERSON><PERSON>',
      avatar: 'https://randomuser.me/api/portraits/women/65.jpg',
      message: 'Accepted Your Request',
      time: '10:25 AM',
      actions: null
    },
    {
      id: 4,
      name: '<PERSON><PERSON> <PERSON>',
      avatar: 'https://randomuser.me/api/portraits/women/68.jpg',
      message: 'Requested To Be Your Fan',
      time: null,
      actions: [
        { label: 'Accept', style: 'bg-blue-500 text-white', onClick: () => {} },
        { label: 'Deny', style: 'bg-gray-200 text-gray-700', onClick: () => {} }
      ]
    },
    {
      id: 5,
      name: 'Nikhil Bejai',
      avatar: 'https://randomuser.me/api/portraits/men/45.jpg',
      message: 'Is Now A Fan Of Your Work! Explore Their Profile To Connect Further.',
      time: '2 hours ago',
      actions: null
    },
    {
      id: 6,
      name: 'Sham Kumar',
      avatar: 'https://randomuser.me/api/portraits/men/46.jpg',
      message: 'Liked Your Skrivee',
      time: '36 min ago',
      actions: null
    }
  ],
  thursday: [
    {
      id: 7,
      name: 'Arjun Sharma',
      avatar: 'https://randomuser.me/api/portraits/men/75.jpg',
      message: 'Started Following Your Work. Check Out Their Profile!',
      time: '09:15 PM',
      actions: null
    },
    {
      id: 8,
      name: 'Priya Mehta',
      avatar: 'https://randomuser.me/api/portraits/women/32.jpg',
      message: 'Commented On Your Latest Skrivee',
      time: '07:45 PM',
      actions: null
    },
    {
      id: 9,
      name: 'Rohit Gupta',
      avatar: 'https://randomuser.me/api/portraits/men/67.jpg',
      message: 'Wants To Collaborate With You',
      time: null,
      actions: [
        { label: 'Accept', style: 'bg-blue-500 text-white', onClick: () => {} },
        { label: 'Deny', style: 'bg-gray-200 text-gray-700', onClick: () => {} }
      ]
    },
    {
      id: 10,
      name: 'Kavya Singh',
      avatar: 'https://randomuser.me/api/portraits/women/89.jpg',
      message: 'Shared Your Skrivee With Their Network',
      time: '02:30 PM',
      actions: null
    },
    {
      id: 11,
      name: 'Vikash Kumar',
      avatar: 'https://randomuser.me/api/portraits/men/54.jpg',
      message: 'Mentioned You In Their Story',
      time: '12:15 PM',
      actions: null
    }
  ]
};

const Notifications = () => {
  return (
    <div className="content-area">
      {/* Header */}
      <div className="notification-header">
        <h2 className="notification-title">Notifications</h2>
        <button className="settings-btn">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="3"></circle>
            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06A1.65 1.65 0 0 0 4.6 15a1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06A1.65 1.65 0 0 0 9 4.6a1.65 1.65 0 0 0 1 1.51V6a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82 1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
          </svg>
        </button>
      </div>

      {/* Notifications Content */}
      <div className="notifications-container">
        {/* Friday Notifications */}
        <div className="date-label">Yesterday | Friday</div>
        <div className="notifications-list">
          {notificationsByDay.friday.map((n) => (
            <div key={n.id} className="notification-item">
              <div className="notification-content">
                <img src={n.avatar} alt={n.name} className="notification-avatar" />
                <div className="notification-text">
                  <div className="notification-name">{n.name}</div>
                  <div className="notification-message">{n.message}</div>
                </div>
              </div>
              <div className="notification-actions">
                {n.actions && n.actions.map((a, i) => (
                  <button
                    key={a.label}
                    className={`action-btn ${a.label.toLowerCase()}-btn`}
                    onClick={a.onClick}
                  >
                    {a.label}
                  </button>
                ))}
                {n.time && <span className="notification-time">{n.time}</span>}
              </div>
            </div>
          ))}
        </div>

        {/* Thursday Notifications */}
        <div className="date-label" style={{marginTop: '24px'}}>Thursday</div>
        <div className="notifications-list">
          {notificationsByDay.thursday.map((n) => (
            <div key={n.id} className="notification-item">
              <div className="notification-content">
                <img src={n.avatar} alt={n.name} className="notification-avatar" />
                <div className="notification-text">
                  <div className="notification-name">{n.name}</div>
                  <div className="notification-message">{n.message}</div>
                </div>
              </div>
              <div className="notification-actions">
                {n.actions && n.actions.map((a, i) => (
                  <button
                    key={a.label}
                    className={`action-btn ${a.label.toLowerCase()}-btn`}
                    onClick={a.onClick}
                  >
                    {a.label}
                  </button>
                ))}
                {n.time && <span className="notification-time">{n.time}</span>}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default Notifications; 