import { useEffect, useState } from 'react';
import ProfileHandling from '../../../action/ProfileHandling';
import EditHeader from './EditHeader';
import EmailVerification from './EmailVerification';
import SocialLinks from './SocialLinks';

function PublicationEdit({ user }) {
    const { fetchProfile, profileLoading, profileUpdate } = ProfileHandling();

    const [formData, setFormData] = useState({
        userName: user.userName,
        publicationName: '',
        bio: '',
        missionStatement: '',
        yearOfEstablishment: '',
        isbnRegistrationNumber: '',
        rniRegistrationNumber: '',
        gstinNumber: '',
        contactEmail: '',
        contactNumber: '',
        location: '',
        language: '',
        // Optional: social links or hidden flags
        social_web_url: '',
        social_insta_url: '',
        social_fb_url: '',
        social_linkedin_url: '',
        social_twitter_url: '',
        rniHidden: true,
        gstinHidden: true,
        social_goodReads_url: ''
    });

    useEffect(() => {
        (async () => {
            const data = await fetchProfile();
            if (Object.keys(data).length > 0) {
                setFormData((prev) => ({
                    ...prev,
                    publicationName: data.publicationName ?? '',
                    bio: data.bio ?? '',
                    missionStatement: data.missionStatement ?? '',
                    yearOfEstablishment: data.yearOfEstablishment ?? '',
                    isbnRegistrationNumber: data.isbnRegistrationNumber ?? '',
                    rniRegistrationNumber: data.rniRegistrationNumber ?? '',
                    gstinNumber: data.gstinNumber ?? '',
                    contactEmail: data.contactEmail ?? '',
                    contactNumber: data.contactNumber ?? '',
                    location: data.location ?? '',
                    language: data.language ?? '',
                    social_web_url: data.social_web_url ?? '',
                    social_insta_url: data.social_insta_url ?? '',
                    social_fb_url: data.social_fb_url ?? '',
                    social_linkedin_url: data.social_linkedin_url ?? '',
                    social_twitter_url: data.social_twitter_url ?? '',
                }));
            }
        })();
    }, []);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setFormData({ ...formData, [name]: value });
    };

    const handleSubmit = (e) => {
        e.preventDefault();
        profileUpdate(formData)
    }

    console.log(formData)

    return (
        <div className="flex-1 w-full px-4 mb-10 bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
            {
                profileLoading ? (
                    <div className="flex justify-center items-center h-64">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#4A99F8]"></div>
                    </div>
                ) : (
                    <>
                        <EditHeader user={user.userName || ''} txt="Library Publication" />
                        {!user.isVerified ? <EmailVerification email={user.email} /> : <></>}
                        <form className="space-y-8 mt-8 bg-white/90 backdrop-blur-sm shadow-xl rounded-2xl md:p-12 md:px-12 p-6 border border-white/20 transition-all duration-300 hover:shadow-2xl hover:shadow-purple-800/50" onSubmit={handleSubmit}>
                            {/* Publication Name & Username */}
                            <div className="md:flex-row flex-col flex gap-6">
                                <div className="w-full md:w-1/2">
                                    <label className="block font-semibold text-gray-700 mb-2">Publication Name <span className="text-red-500">*</span></label>
                                    <input type="text" autoComplete='off' name="publicationName" value={formData.publicationName} onChange={handleChange}
                                        className="input w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#4A99F8] focus:border-transparent transition-all duration-200 bg-gray-50/50 hover:bg-white hover:shadow-sm"
                                        placeholder="Enter Name" required />
                                </div>
                                <div className="w-full md:w-1/2">
                                    <label className="block font-semibold text-gray-700 mb-2">Username <span className="text-red-500">*</span></label>
                                    <input type="text" autoComplete='off' name="userName" value={formData.userName} onChange={handleChange}
                                        className="input w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#4A99F8] focus:border-transparent transition-all duration-200 bg-gray-50/50 hover:bg-white hover:shadow-sm"
                                        placeholder="Enter Username" required />
                                </div>
                            </div>

                            {/* Bio */}
                            <div>
                                <label className="block font-semibold text-gray-700 mb-2">Bio <span className="text-red-500">*</span></label>
                                <textarea
                                    autoComplete='off'
                                    name="bio"
                                    value={formData.bio}
                                    onChange={handleChange}
                                    maxLength={150}
                                    className={`textarea w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 transition-all duration-200 bg-gray-50/50 hover:bg-white hover:shadow-sm resize-none ${formData.bio.length > 150 ? 'ring-red-500 border-red-300' : 'focus:ring-[#4A99F8] focus:border-transparent'}`}
                                    rows={4}
                                    placeholder="Enter a short Bio"
                                ></textarea>
                                <div className={`text-sm text-right mt-1 font-medium ${formData.bio.length >= 150 ? 'text-red-500' : 'text-gray-500'}`}>
                                    {formData.bio.length}/150 Characters
                                </div>
                            </div>

                            {/* Mission Statement */}
                            <div>
                                <label className="block font-semibold text-gray-700 mb-2">Mission Statement <span className="text-red-500">*</span></label>
                                <textarea
                                    name="missionStatement"
                                    autoComplete='off'
                                    value={formData.missionStatement}
                                    onChange={handleChange}
                                    maxLength={250}
                                    className="textarea w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#4A99F8] focus:border-transparent transition-all duration-200 bg-gray-50/50 hover:bg-white hover:shadow-sm resize-none"
                                    rows={3}
                                    placeholder="What's your mission?"
                                    required
                                ></textarea>
                                <div className={`text-sm text-right mt-1 font-medium ${formData.missionStatement.length >= 250 ? 'text-red-500' : 'text-gray-500'}`}>
                                    {formData.missionStatement.length}/250 Characters
                                </div>
                            </div>

                            {/* Year of Establishment & ISBN Registration Number */}
                            <div className="flex gap-6 flex-col md:flex-row">
                                <div className="flex-1">
                                    <label className="block font-semibold text-gray-700 mb-2">Year of Establishment <span className="text-red-500">*</span></label>
                                    <input type="date" name="yearOfEstablishment" value={formData.yearOfEstablishment} onChange={handleChange}
                                        className="input w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#4A99F8] focus:border-transparent transition-all duration-200 bg-gray-50/50 hover:bg-white hover:shadow-sm" required />
                                </div>
                                <div className="flex-1">
                                    <label className="block font-semibold text-gray-700 mb-2">ISBN Registration Number <span className="text-red-500">*</span></label>
                                    <input type="text" autoComplete='off'
                                        name="isbnRegistrationNumber" value={formData.isbnRegistrationNumber} onChange={handleChange}
                                        className="input w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#4A99F8] focus:border-transparent transition-all duration-200 bg-gray-50/50 hover:bg-white hover:shadow-sm"
                                        placeholder="Enter ISBN number" required />
                                </div>
                            </div>

                            {/* RNI Registration Number & GSTIN Number */}
                            <div className="flex gap-6 flex-col md:flex-row">
                                <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-2">
                                        <label className="block font-semibold text-gray-700">RNI Registration Number <span className="text-red-500">*</span></label>
                                        <label className="flex items-center gap-1 cursor-pointer text-sm text-gray-600">
                                            <input
                                                type="checkbox"
                                                checked={formData.rniHidden}
                                                onChange={(e) => setFormData({...formData, rniHidden: e.target.checked})}
                                                className="w-4 h-4 text-[#4A99F8] border-gray-300 rounded focus:ring-[#4A99F8]"
                                            />
                                            Hide
                                        </label>
                                    </div>
                                    <input type="text" autoComplete='off' name="rniRegistrationNumber" value={formData.rniRegistrationNumber} onChange={handleChange}
                                        className="input w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#4A99F8] focus:border-transparent transition-all duration-200 bg-gray-50/50 hover:bg-white hover:shadow-sm"
                                        placeholder="Enter RNI number" required />
                                </div>
                                <div className="flex-1">
                                    <div className="flex items-center gap-2 mb-2">
                                        <label className="block font-semibold text-gray-700">GSTIN Number <span className="text-red-500">*</span></label>
                                        <label className="flex items-center gap-1 cursor-pointer text-sm text-gray-600">
                                            <input
                                                type="checkbox"
                                                checked={formData.gstinHidden}
                                                onChange={(e) => setFormData({...formData, gstinHidden: e.target.checked})}
                                                className="w-4 h-4 text-[#4A99F8] border-gray-300 rounded focus:ring-[#4A99F8]"
                                            />
                                            Hide
                                        </label>
                                    </div>
                                    <input type="text" autoComplete='off'
                                        name="gstinNumber" value={formData.gstinNumber} onChange={handleChange}
                                        className="input w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#4A99F8] focus:border-transparent transition-all duration-200 bg-gray-50/50 hover:bg-white hover:shadow-sm"
                                        placeholder="Enter GSTIN number" required />
                                </div>
                            </div>

                            {/* Contact Email & Contact Number */}
                            <div className="flex gap-6 md:flex-row flex-col">
                                <div className="flex-1">
                                    <label className="block font-semibold text-gray-700 mb-2">Contact Email <span className="text-red-500">*</span></label>
                                    <input type="email" name="contactEmail" value={formData.contactEmail} onChange={handleChange}
                                        className="input w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#4A99F8] focus:border-transparent transition-all duration-200 bg-gray-50/50 hover:bg-white hover:shadow-sm"
                                        placeholder="<EMAIL>" required />
                                </div>
                                <div className="flex-1">
                                    <label className="block font-semibold text-gray-700 mb-2">Contact Number <span className="text-red-500">*</span></label>
                                    <input type="tel" autoComplete='off' name="contactNumber" value={formData.contactNumber} onChange={handleChange}
                                        className="input w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#4A99F8] focus:border-transparent transition-all duration-200 bg-gray-50/50 hover:bg-white hover:shadow-sm"
                                        placeholder="+91 98765 43210" required />
                                </div>
                            </div>

                            {/* Location & Language */}
                            <div className="flex gap-6 md:flex-row flex-col">
                                <div className="flex-1">
                                    <label className="block font-semibold text-gray-700 mb-2">Location <span className="text-red-500">*</span></label>
                                    <input type="text" autoComplete='off' name="location" value={formData.location} onChange={handleChange}
                                        className="input w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#4A99F8] focus:border-transparent transition-all duration-200 bg-gray-50/50 hover:bg-white hover:shadow-sm"
                                        placeholder="Your city, country" required />
                                </div>
                                <div className="flex-1">
                                    <label className="block font-semibold text-gray-700 mb-2">Language <span className="text-red-500">*</span></label>
                                    <select name="language" value={formData.language} onChange={handleChange}
                                        className="input w-full border border-gray-300 rounded-lg px-4 py-3 focus:outline-none focus:ring-2 focus:ring-[#4A99F8] focus:border-transparent transition-all duration-200 bg-gray-50/50 hover:bg-white hover:shadow-sm"
                                        required>
                                        <option value="">Select Language</option>
                                        <option value="English">English</option>
                                        <option value="Hindi">Hindi</option>
                                        <option value="Bengali">Bengali</option>
                                        <option value="Telugu">Telugu</option>
                                        <option value="Marathi">Marathi</option>
                                        <option value="Tamil">Tamil</option>
                                        <option value="Gujarati">Gujarati</option>
                                        <option value="Urdu">Urdu</option>
                                        <option value="Kannada">Kannada</option>
                                        <option value="Odia">Odia</option>
                                        <option value="Malayalam">Malayalam</option>
                                        <option value="Punjabi">Punjabi</option>
                                        <option value="Assamese">Assamese</option>
                                        <option value="Sanskrit">Sanskrit</option>
                                    </select>
                                </div>
                            </div>

                            <SocialLinks formData={formData} onChange={(socialData) => {
                                setFormData(prev => ({ ...prev, ...socialData }));
                            }} />

                            <div className="flex justify-center items-center pt-6 border-t border-gray-200">
                                <div className="flex items-center justify-center mt-6 gap-3">
                                    <div className="md:w-32 w-20 h-1 rounded-l-full bg-gradient-to-r from-[#4A99F8] to-blue-500 transform -translate-y-2" />
                                    <button
                                        type="submit"
                                        className="relative transform -translate-y-2 bg-gradient-to-r from-[#4A99F8] to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold py-3 px-8 rounded-lg transition-all duration-200 shadow-lg hover:shadow-xl hover:scale-105 active:scale-95"
                                    >
                                        Save Changes
                                    </button>
                                    <div className="md:w-32 w-20 h-1 rounded-r-full transform -translate-y-2 bg-gradient-to-r from-[#D99FE9] to-purple-400" />
                                </div>
                            </div>
                        </form>
                    </>
                )
            }
        </div>
    );
}

export default PublicationEdit;