/* Notifications Page Styles */

/* Content Area */
.content-area {
  width: 100%;
  height: calc(100vh - 100px);
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  flex-direction: column;
}

/* Header */
.notification-header {
  background: linear-gradient(135deg, #4A99F8 0%, #0A06F4 100%);
  padding: 14px 24px;
  color: white;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.notification-title {
  font-size: 24px;
  font-weight: 700;
  margin: 0;
  color: white;
}

.settings-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  border-radius: 8px;
  padding: 8px;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.settings-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: rotate(90deg);
}

/* Notifications Container */
.notifications-container {
  flex: 1;
  padding: 20px 24px;
  overflow-y: auto;
  min-height: 0;
}

/* Date Label */
.date-label {
  color: #6b7280;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 16px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Notifications List */
.notifications-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Individual Notification Item */
.notification-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 12px;
  padding: 16px;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.notification-item:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
  border-color: #4A99F8;
}

/* Notification Content */
.notification-content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.notification-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
}

.notification-item:hover .notification-avatar {
  border-color: #4A99F8;
  transform: scale(1.05);
}

.notification-text {
  flex: 1;
}

.notification-name {
  font-weight: 600;
  font-size: 16px;
  color: #1f2937;
  margin-bottom: 4px;
}

.notification-message {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.4;
}

/* Notification Actions */
.notification-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-btn {
  padding: 6px 16px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.accept-btn {
  background: #4A99F8;
  color: white;
}

.accept-btn:hover {
  background: #0A06F4;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(74, 153, 248, 0.4);
}

.deny-btn {
  background: #f3f4f6;
  color: #6b7280;
}

.deny-btn:hover {
  background: #e5e7eb;
  color: #374151;
}

.notification-time {
  font-size: 12px;
  color: #9ca3af;
  white-space: nowrap;
  margin-left: 8px;
}

/* Custom Scrollbar */
.notifications-container::-webkit-scrollbar {
  width: 8px;
}

.notifications-container::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.notifications-container::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #4A99F8, #0A06F4);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.notifications-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #0A06F4, #4A99F8);
}

/* Animation for notification items */
.notification-item {
  animation: fadeInUp 0.5s ease forwards;
  opacity: 0;
  transform: translateY(20px);
}

.notification-item:nth-child(1) { animation-delay: 0.1s; }
.notification-item:nth-child(2) { animation-delay: 0.2s; }
.notification-item:nth-child(3) { animation-delay: 0.3s; }
.notification-item:nth-child(4) { animation-delay: 0.4s; }
.notification-item:nth-child(5) { animation-delay: 0.5s; }
.notification-item:nth-child(6) { animation-delay: 0.6s; }

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .notification-header {
    padding: 16px 20px;
  }

  .notification-title {
    font-size: 20px;
  }

  .notifications-container {
    padding: 16px 20px;
  }

  .notification-item {
    padding: 12px;
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .notification-content {
    width: 100%;
  }

  .notification-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .notification-avatar {
    width: 40px;
    height: 40px;
  }

  .notification-name {
    font-size: 14px;
  }

  .notification-message {
    font-size: 13px;
  }
}
