import cookie from 'js-cookie';
import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Route, Routes, useLocation, useNavigate } from 'react-router-dom';

import { fetchMe } from './action/userActions';
import AdsOutlet from './AdsOutlet';
import Login from './pages/Auth/Login';
import SignUp from './pages/Auth/SignUp';
import AuthorRanking from './pages/authors/AuthorRanking';
import AuthorsHomepage from './pages/authors/AuthorsHomepage';
import Home from './pages/home/<USER>';
import Notifications from './pages/home/<USER>';
import Landing from './pages/landing/Landing';
import Messages from './pages/messages/Messages';
import AuthorPitch from './pages/pitch/AuthorPitch';
import Pitch from './pages/pitch/Pitch';
import AuthorEdit from './pages/profile/editProfile/AuthorEdit';
import ProfileEdit from './pages/profile/editProfile/ProfileEdit';
import PublicationEdit from './pages/profile/editProfile/PublicationEdit';
import MemberProfile from './pages/profile/MemberProfile';
import MyProfilePage from './pages/profile/MyProfilePage';
import Profile from './pages/profile/Profile';
import SettingsPage from './pages/settings/SettingsPage';
import Skrivee from './pages/skrivee/Skrivee';
import Verification from './pages/verification/Verification';
import ProtectedRoute from './ProtectedRoute';




function App() {
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const {isAuthenticated,user,userLoading,userError} = useSelector((state)=>state.user)
  const [appLoading,setAppLoading] = useState(true);
  const location = useLocation();
  useEffect(() => {
    const token = cookie.get('token');
    if (token && !isAuthenticated) {
      dispatch(fetchMe());
    }
    else {
      setAppLoading(false)
    }
  }, [dispatch,cookie.get('token')]);

  useEffect(() => {
    if(isAuthenticated && location.pathname == "/verify-email") {
          setAppLoading(false)
          return;
        }
    if (isAuthenticated) {
        if(!user?.isProfileComplete || !user?.isVerified) navigate('/profile/edit');
        else if(location.pathname === "/" || location.pathname === "/signup/:role" || location.pathname === "/login" ) navigate("/profile/edit");
        setAppLoading(false);
    }
    if(userError ) setAppLoading(false);
  }, [isAuthenticated,userLoading,userError]);

  if(appLoading) return(
    <div className="flex items-center justify-center min-h-screen bg-gray-100">
      <div className="text-xl font-semibold text-gray-700">Loading...</div>
    </div>
  )
  const memoizedAdsOutlet = <AdsOutlet />
  return (
    <div className="bg-[#F8F8F8] min-h-full ">
      <Routes>
        {/* Public Routes */}
        <Route path="/" element={<Landing />} />
        <Route path="/login" element={<Login />} />
        <Route path="/signup/:role" element={<SignUp />} />
        <Route path="/verify-email" element={<Verification />} />


        {/* Protected Routes - Authentication removed for development */}
        <Route element={<ProtectedRoute />}>
        <Route element={memoizedAdsOutlet}>
          <Route path="/home" element={<Home />} />
          <Route path="/profile" element={<Profile />} />
          <Route path="/memberProfile" element={<MemberProfile />} />
          <Route path="/myProfile" element={<MyProfilePage />} />
          <Route path='/profile/edit' element={<ProfileEdit/>}/>
          <Route path='/profile/editProfile' element={<AuthorEdit user={{userName: 'Ravi_Agarwal', email: '<EMAIL>', isVerified: true}}/>}/>
          <Route path='/profile/editPublication' element={<PublicationEdit user={{userName: 'Library_Publication', email: '<EMAIL>', isVerified: true}}/>}/>
          <Route path="/authors" element={<AuthorsHomepage />} />
          <Route path="/ranking" element={<AuthorRanking />} />
          <Route path="/pitch" element={<Pitch />} />
          <Route path="/author-pitch" element={<AuthorPitch />} />
          <Route path="/settings" element={<SettingsPage />} />
          <Route path="/notifications" element={<Notifications />} />
          </Route>
          <Route path="/skrivee" element={<Skrivee />} />
          <Route path="/messages" element={<Messages />} />
        </Route>
      </Routes>
    </div>
  );
}

export default App;
