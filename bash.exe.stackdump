Stack trace:
Frame         Function      Args
0007FFFF9960  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8860) msys-2.0.dll+0x2118E
0007FFFF9960  0002100469BA (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x69BA
0007FFFF9960  0002100469F2 (00021028DF99, 0007FFFF9818, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9960  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9960  00021006A545 (0007FFFF9970, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF9970, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF92A6C0000 ntdll.dll
7FF928BA0000 KERNEL32.DLL
7FF927E40000 KERNELBASE.dll
7FF9299B0000 USER32.dll
7FF927C90000 win32u.dll
7FF929B80000 GDI32.dll
7FF927940000 gdi32full.dll
7FF9277F0000 msvcp_win.dll
7FF927B40000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF929770000 advapi32.dll
7FF929490000 msvcrt.dll
7FF928FA0000 sechost.dll
7FF929D80000 RPCRT4.dll
7FF926DC0000 CRYPTBASE.DLL
7FF9278A0000 bcryptPrimitives.dll
7FF929D40000 IMM32.DLL
