import { useState } from 'react';
import { BiGlobe } from 'react-icons/bi';
import { FaFacebook, FaHeart, FaInstagram, FaLinkedin, FaTwitter } from 'react-icons/fa';
import { SiGoodreads } from 'react-icons/si';
import { useNavigate } from 'react-router-dom';

function MyProfilePage({ user }) {
  const navigate = useNavigate();
  const [showFansModal, setShowFansModal] = useState(false);
  const [currentView, setCurrentView] = useState('info'); // 'info' or 'fans'

  // Sample fans data
  const fansData = [
    {
      id: 1,
      name: '<PERSON>',
      username: '@ralph_edwards',
      avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>',
      fans: '5.5K',
      faves: '550',
      skrivee: '20',
      bio: 'Passionate storyteller weaving tales, poems, and blogs that inspire and resonate. Exploring life through words, one story at a time.',
      isFollowing: false
    },
    {
      id: 2,
      name: '<PERSON>',
      username: '@esther_howard',
      avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>',
      fans: '5.5K',
      faves: '550',
      skrivee: '20',
      bio: 'Passionate storyteller weaving tales, poems, and blogs that inspire and resonate. Exploring life through words, one story at a time.',
      isFollowing: false
    },
    {
      id: 3,
      name: 'Darlene Robertson',
      username: '@darlene_robertson',
      avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>',
      fans: '5.5K',
      faves: '550',
      skrivee: '20',
      bio: 'Passionate storyteller weaving tales, poems, and blogs that inspire and resonate. Exploring life through words, one story at a time.',
      isFollowing: false
    },
    {
      id: 4,
      name: 'Eleanor Pena',
      username: '@eleanor_pena',
      avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>',
      fans: '5.5K',
      faves: '550',
      skrivee: '20',
      bio: 'Passionate storyteller weaving tales, poems, and blogs that inspire and resonate. Exploring life through words, one story at a time.',
      isFollowing: false
    },
    {
      id: 5,
      name: 'Wade Warren',
      username: '@wade_warren',
      avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>',
      fans: '5.5K',
      faves: '550',
      skrivee: '20',
      bio: 'Passionate storyteller weaving tales, poems, and blogs that inspire and resonate. Exploring life through words, one story at a time.',
      isFollowing: false
    },
    {
      id: 6,
      name: 'Annette Black',
      username: '@annette_black',
      avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>',
      fans: '5.5K',
      faves: '550',
      skrivee: '20',
      bio: 'Passionate storyteller weaving tales, poems, and blogs that inspire and resonate. Exploring life through words, one story at a time.',
      isFollowing: false
    },
    {
      id: 7,
      name: 'Jacob Jones',
      username: '@jacob_jones',
      avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>',
      fans: '5.5K',
      faves: '550',
      skrivee: '20',
      bio: 'Passionate storyteller weaving tales, poems, and blogs that inspire and resonate. Exploring life through words, one story at a time.',
      isFollowing: false
    },
    {
      id: 8,
      name: 'Theresa Webb',
      username: '@theresa_webb',
      avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>',
      fans: '5.5K',
      faves: '550',
      skrivee: '20',
      bio: 'Passionate storyteller weaving tales, poems, and blogs that inspire and resonate. Exploring life through words, one story at a time.',
      isFollowing: false
    }
  ];

  const profileData = {
    username: 'Ravi.Agarwal',
    fullName: 'Ravi Agarwal',
    rank: 1800,
    fans: 250,
    faves: 550,
    bio: 'I am a passionate writer with a keen eye for detail, dedicated to exploring the complexities of the human experience through captivating storytelling that leaves a lasting impression.',
    passion: 'Lorem ipsum dolor sit amet consectetur. Enim rhoncus blandit consequat vel nulla at feugiat volutpat.',
    gender: 'Male',
    dob: '04-02-1991',
    mobile: '+012345678',
    occupation: 'Student',
    email: '<EMAIL>',
    location: 'New Delhi',
    language: 'English',
    social: {
      instagram: 'Ravi Agarwal',
      facebook: 'Ravi Agarwal',
      linkedin: 'Ravi Agarwal',
      twitter: 'Ravi Agarwal',
      goodreads: 'Ravi Agarwal',
    }
  };

  return (
    <>
      <style jsx>{`
        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }
        @keyframes slideInUp {
          from {
            opacity: 0;
            transform: translateY(30px) scale(0.95);
          }
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }
        .line-clamp-2 {
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
      `}</style>
      <div className="flex-1 w-full bg-gradient-to-br from-indigo-50 via-white to-cyan-50 min-h-screen relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-cyan-400/10 to-blue-400/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-purple-400/5 to-pink-400/5 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      {/* Profile Header */}
      <div className="bg-white/90 backdrop-blur-md p-4 sm:p-6 lg:p-8 border-b border-gray-200/50 text-center shadow-2xl relative overflow-hidden">
        {/* Enhanced Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-r from-blue-50/40 to-purple-50/40"></div>
        <div className="absolute inset-0 bg-gradient-to-br from-transparent via-white/20 to-transparent"></div>
        <div className="relative z-10">
          <div className="flex justify-center gap-8 sm:gap-12 lg:gap-16 items-center mb-4 sm:mb-6">
            {/* Fans */}
            <div className="group cursor-pointer relative" onClick={() => setCurrentView('fans')}>
              <div className="absolute inset-0 bg-gradient-to-br from-blue-100/50 to-purple-100/50 rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-500 transform scale-95 group-hover:scale-100"></div>
              <div className="relative z-10 p-4 rounded-2xl">
                <div className="text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent group-hover:from-blue-500 group-hover:to-purple-500 transition-all duration-300 group-hover:scale-110">
                  {profileData.fans}
                </div>
                <div className="text-xs sm:text-sm text-gray-600 font-semibold tracking-wider">FANS</div>
                <div className="w-8 sm:w-10 lg:w-12 h-1 bg-gradient-to-r from-[#4A99F8] to-purple-500 mx-auto mt-2 rounded-full transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 shadow-lg"></div>
              </div>
            </div>

            {/* Circular Profile Image */}
            <div className="relative group">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full p-1 opacity-0 group-hover:opacity-100 transition-all duration-500 animate-pulse"></div>
              <div className="w-20 h-20 sm:w-24 sm:h-24 lg:w-32 lg:h-32 rounded-full overflow-hidden border-4 border-white shadow-2xl group-hover:shadow-3xl transition-all duration-500 group-hover:scale-105 relative z-10">
                <img
                  src={user?.profileImage || "https://img.daisyui.com/images/profile/demo/<EMAIL>"}
                  alt={profileData.fullName}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
                />
              </div>
              <div className="absolute inset-0 rounded-full bg-gradient-to-tr from-[#4A99F8]/30 to-purple-500/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="absolute -inset-2 bg-gradient-to-r from-blue-400/20 to-purple-500/20 rounded-full blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </div>

            {/* Faves */}
            <div className="group cursor-pointer relative" onClick={() => setCurrentView('faves')}>
              <div className="absolute inset-0 bg-gradient-to-br from-purple-100/50 to-pink-100/50 rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-500 transform scale-95 group-hover:scale-100"></div>
              <div className="relative z-10 p-4 rounded-2xl">
                <div className="text-2xl sm:text-3xl lg:text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent group-hover:from-purple-500 group-hover:to-pink-500 transition-all duration-300 group-hover:scale-110">
                  {profileData.faves}
                </div>
                <div className="text-xs sm:text-sm text-gray-600 font-semibold tracking-wider">FAVES</div>
                <div className="w-8 sm:w-10 lg:w-12 h-1 bg-gradient-to-r from-purple-500 to-pink-500 mx-auto mt-2 rounded-full transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 shadow-lg"></div>
              </div>
            </div>
          </div>

          {/* Username + Rank */}
          <div className="text-xs sm:text-sm text-gray-500 mb-1 sm:mb-2 font-medium">Rank {profileData.rank}</div>
          <h2 className="text-xl sm:text-2xl lg:text-3xl font-bold mb-4 sm:mb-6 text-gray-800 hover:text-[#4A99F8] transition-colors duration-300">{profileData.username}</h2>

          {/* Edit Profile Button */}
          <div className="flex justify-center mt-4 sm:mt-6 mb-6 sm:mb-8 px-4 sm:px-0">
            <button className="group relative flex items-center justify-center gap-2 sm:gap-3 bg-[#4A99F8] hover:bg-[#0A06F4] text-white px-8 sm:px-10 py-3 sm:py-4 rounded-full text-sm font-bold shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-500 overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <svg className="w-4 h-4 sm:w-5 sm:h-5 group-hover:rotate-12 transition-transform duration-500 relative z-10" fill="currentColor" viewBox="0 0 20 20">
                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
              </svg>
              <span className="relative z-10">Edit Profile</span>
              <div className="absolute inset-0 rounded-full bg-gradient-to-r from-blue-400/30 to-blue-500/30 blur-lg opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </button>
          </div>

          {/* Bio */}
          <p className="text-gray-700 text-xs sm:text-sm max-w-xs sm:max-w-2xl lg:max-w-3xl mx-auto mb-6 sm:mb-8 leading-relaxed bg-white/50 backdrop-blur-sm p-3 sm:p-4 rounded-lg shadow-sm border border-gray-100">
            {profileData.bio}
          </p>

          {/* Tabs */}
          <div className="flex flex-col sm:flex-row justify-between items-center gap-4 sm:gap-6 max-w-xs sm:max-w-md lg:max-w-lg mx-auto px-4 sm:px-0">
            <button
              onClick={() => setCurrentView('info')}
              className="relative group bg-[#4A99F8] hover:bg-[#0A06F4] text-white px-6 py-3 rounded-xl text-sm font-bold shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-500 min-w-[100px] overflow-hidden"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <span className="relative z-10">INFO</span>
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-400/30 to-purple-500/30 blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </button>
            <button className="relative group bg-[#4A99F8] hover:bg-[#0A06F4] text-white px-6 py-3 rounded-xl text-sm font-bold shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-500 min-w-[100px] overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <span className="relative z-10">SKRIVEE</span>
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-400/30 to-blue-500/30 blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </button>
            <button className="relative group bg-[#4A99F8] hover:bg-[#0A06F4] text-white px-6 py-3 rounded-xl text-sm font-bold shadow-2xl hover:shadow-3xl transform hover:scale-110 transition-all duration-500 min-w-[100px] overflow-hidden">
              <div className="absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <span className="relative z-10">FEEDBACK</span>
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-400/30 to-blue-500/30 blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex justify-center p-4 sm:p-6 lg:p-8">
        {currentView === 'info' ? (
          /* Info Card */
          <div className="bg-white/95 backdrop-blur-lg rounded-3xl border border-gray-200/50 p-6 sm:p-8 lg:p-10 w-full max-w-xs sm:max-w-lg lg:max-w-2xl xl:max-w-3xl shadow-2xl hover:shadow-3xl transition-all duration-700 relative overflow-hidden group">
            {/* Enhanced Background Pattern */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-50/40 via-purple-50/20 to-pink-50/40 opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
            <div className="absolute top-0 left-0 w-full h-1 bg-[#4A99F8]"></div>
            <div className="absolute -top-20 -right-20 w-40 h-40 bg-gradient-to-br from-blue-400/10 to-purple-400/10 rounded-full blur-2xl"></div>
            <div className="absolute -bottom-20 -left-20 w-40 h-40 bg-gradient-to-tr from-purple-400/10 to-pink-400/10 rounded-full blur-2xl"></div>

            <div className="relative z-10">
              <div className="flex items-center justify-between mb-6 sm:mb-8">
                <h3 className="font-bold text-lg sm:text-xl lg:text-2xl text-gray-800 hover:text-[#4A99F8] transition-colors duration-300">{profileData.fullName}</h3>
                <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-[#2E3A59] to-[#4A99F8] rounded-full flex items-center justify-center text-white shadow-lg hover:shadow-xl transform hover:scale-110 hover:rotate-12 transition-all duration-300 cursor-pointer">
                  <BiGlobe size={16} className="sm:w-5 sm:h-5" />
                </div>
              </div>

              <div className="space-y-4 sm:space-y-6 text-xs sm:text-sm">
                <div className="group">
                  <strong className="block text-gray-800 font-bold mb-1 sm:mb-2 text-sm sm:text-base group-hover:text-[#4A99F8] transition-colors duration-300">Passion</strong>
                  <p className="text-gray-700 leading-relaxed bg-gradient-to-r from-blue-50 to-purple-50 p-3 sm:p-4 rounded-lg border-l-4 border-[#4A99F8] hover:shadow-md transition-all duration-300">{profileData.passion}</p>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                  <div className="group p-3 sm:p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                    <strong className="block text-gray-800 font-bold mb-1 sm:mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Gender</strong>
                    <p className="text-gray-700 font-medium">{profileData.gender}</p>
                  </div>
                  <div className="group p-3 sm:p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                    <strong className="block text-gray-800 font-bold mb-1 sm:mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Date of Birth</strong>
                    <p className="text-gray-700 font-medium">{profileData.dob}</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                  <div className="group p-3 sm:p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                    <strong className="block text-gray-800 font-bold mb-1 sm:mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Mobile Number</strong>
                    <p className="text-gray-700 font-medium break-all">{profileData.mobile}</p>
                  </div>
                  <div className="group p-3 sm:p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                    <strong className="block text-gray-800 font-bold mb-1 sm:mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Occupation</strong>
                    <p className="text-gray-700 font-medium">{profileData.occupation}</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                  <div className="group p-3 sm:p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                    <strong className="block text-gray-800 font-bold mb-1 sm:mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Email Id</strong>
                    <p className="text-gray-700 font-medium break-all">{profileData.email}</p>
                  </div>
                  <div className="group p-3 sm:p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                    <strong className="block text-gray-800 font-bold mb-1 sm:mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Location</strong>
                    <p className="text-gray-700 font-medium">{profileData.location}</p>
                  </div>
                </div>

                <div className="group p-3 sm:p-4 rounded-lg hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 transition-all duration-300 hover:shadow-md">
                  <strong className="block text-gray-800 font-bold mb-1 sm:mb-2 group-hover:text-[#4A99F8] transition-colors duration-300">Language</strong>
                  <p className="text-gray-700 font-medium">{profileData.language}</p>
                </div>
              </div>
            </div>
          </div>
        ) : currentView === 'fans' ? (
          /* Fans Content */
          <div className="w-full max-w-6xl">
            {/* Fans Header */}
            <div className="bg-white/95 backdrop-blur-lg rounded-3xl border border-gray-200/50 p-6 sm:p-8 lg:p-10 shadow-2xl hover:shadow-3xl transition-all duration-700 relative overflow-hidden group mb-6">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-50/40 via-purple-50/20 to-pink-50/40 opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
              <div className="absolute top-0 left-0 w-full h-1 bg-[#4A99F8]"></div>
              <div className="relative z-10 flex items-center justify-between">
                <h2 className="text-2xl font-bold text-gray-800">FANS, Ravi Agarwal</h2>
                <div className="flex items-center gap-4">
                  <span className="text-sm text-gray-600">Sort by</span>
                  <select className="bg-white border border-gray-300 rounded-lg px-3 py-1 text-sm text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="popular">Popular</option>
                    <option value="recent">Recent</option>
                    <option value="alphabetical">A-Z</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Fans Grid - 2 cards per row */}
            <div className="max-h-screen overflow-y-auto grid grid-cols-1 md:grid-cols-2 gap-6">
              {fansData.map((fan, index) => (
                <div
                  key={fan.id}
                  onClick={() => navigate('/memberProfile')}
                  className="bg-white/95 backdrop-blur-lg rounded-2xl border border-gray-200/50 p-6 shadow-xl hover:shadow-2xl transition-all duration-500 relative overflow-hidden group cursor-pointer"
                  style={{
                    animation: `slideInUp 0.5s ease-out ${index * 0.1}s forwards`
                  }}
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-50/40 via-purple-50/20 to-pink-50/40 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="absolute top-0 left-0 w-full h-1 bg-[#4A99F8]"></div>

                  <div className="relative z-10">
                    {/* Fan Header */}
                    <div className="flex items-start gap-4 mb-4">
                      <div className="relative">
                        <img
                          src={fan.avatar}
                          alt={fan.name}
                          className="w-14 h-14 rounded-full object-cover border-2 border-white shadow-lg"
                        />
                        <div className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                          <FaHeart className="text-white text-xs" />
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-bold text-lg text-gray-800">{fan.name}</h3>
                        <p className="text-gray-500 text-sm mb-2">{fan.username}</p>
                        <p className="text-gray-600 text-sm leading-relaxed line-clamp-2">{fan.bio}</p>
                      </div>
                      <div className="relative">
                        <button className="text-gray-400 hover:text-gray-600 transition-colors duration-200">
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                          </svg>
                        </button>
                      </div>
                    </div>

                    {/* Fan Stats */}
                    <div className="flex justify-between items-center mb-4">
                      <div className="text-center">
                        <div className="font-bold text-lg text-gray-800">{fan.fans}</div>
                        <div className="text-xs text-gray-500 font-semibold">Fans</div>
                      </div>
                      <div className="text-center">
                        <div className="font-bold text-lg text-gray-800">{fan.faves}</div>
                        <div className="text-xs text-gray-500 font-semibold">Faves</div>
                      </div>
                      <div className="text-center">
                        <div className="font-bold text-lg text-gray-800">{fan.skrivee}</div>
                        <div className="text-xs text-gray-500 font-semibold">Skrivee</div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          // Handle follow action here
                          console.log('Follow clicked for:', fan.name);
                        }}
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-semibold hover:bg-blue-700 transition-all duration-300 flex-1"
                      >
                        Follow
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          // Handle message action here
                          console.log('Message clicked for:', fan.name);
                        }}
                        className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm font-semibold hover:bg-gray-200 transition-all duration-300 flex-1"
                      >
                        Message
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate('/memberProfile');
                        }}
                        className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm font-semibold hover:bg-gray-200 transition-all duration-300 flex-1"
                      >
                        Profile
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          /* Faves Content */
          <div className="w-full max-w-6xl">
            {/* Faves Header */}
            <div className="bg-white/95 backdrop-blur-lg rounded-3xl border border-gray-200/50 p-6 sm:p-8 lg:p-10 shadow-2xl hover:shadow-3xl transition-all duration-700 relative overflow-hidden group mb-6">
              <div className="absolute inset-0 bg-gradient-to-br from-blue-50/40 via-purple-50/20 to-pink-50/40 opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
              <div className="absolute top-0 left-0 w-full h-1 bg-[#4A99F8]"></div>
              <div className="relative z-10 flex items-center justify-between">
                <h2 className="text-2xl font-bold text-gray-800">FAVES, Ravi Agarwal</h2>
                <div className="flex items-center gap-4">
                  <span className="text-sm text-gray-600">Sort by</span>
                  <select className="bg-white border border-gray-300 rounded-lg px-3 py-1 text-sm text-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="popular">Popular</option>
                    <option value="recent">Recent</option>
                    <option value="alphabetical">A-Z</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Faves Grid - 2 cards per row */}
            <div className="max-h-screen overflow-y-auto grid grid-cols-1 md:grid-cols-2 gap-6">
              {fansData.map((fan, index) => (
                <div
                  key={fan.id}
                  onClick={() => navigate('/memberProfile')}
                  className="bg-white/95 backdrop-blur-lg rounded-2xl border border-gray-200/50 p-6 shadow-xl hover:shadow-2xl transition-all duration-500 relative overflow-hidden group cursor-pointer"
                  style={{
                    animation: `slideInUp 0.5s ease-out ${index * 0.1}s forwards`
                  }}
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-50/40 via-purple-50/20 to-pink-50/40 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="absolute top-0 left-0 w-full h-1 bg-[#4A99F8]"></div>

                  <div className="relative z-10">
                    {/* Fan Header */}
                    <div className="flex items-start gap-4 mb-4">
                      <div className="relative">
                        <img
                          src={fan.avatar}
                          alt={fan.name}
                          className="w-14 h-14 rounded-full object-cover border-2 border-white shadow-lg"
                        />
                        <div className="absolute -top-1 -right-1 w-5 h-5 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                          <FaHeart className="text-white text-xs" />
                        </div>
                      </div>
                      <div className="flex-1 min-w-0">
                        <h3 className="font-bold text-lg text-gray-800">{fan.name}</h3>
                        <p className="text-gray-500 text-sm mb-2">{fan.username}</p>
                        <p className="text-gray-600 text-sm leading-relaxed line-clamp-2">{fan.bio}</p>
                      </div>
                      <div className="relative">
                        <button className="text-gray-400 hover:text-gray-600 transition-colors duration-200">
                          <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z" />
                          </svg>
                        </button>
                      </div>
                    </div>

                    {/* Fan Stats */}
                    <div className="flex justify-between items-center mb-4">
                      <div className="text-center">
                        <div className="font-bold text-lg text-gray-800">{fan.fans}</div>
                        <div className="text-xs text-gray-500 font-semibold">Fans</div>
                      </div>
                      <div className="text-center">
                        <div className="font-bold text-lg text-gray-800">{fan.faves}</div>
                        <div className="text-xs text-gray-500 font-semibold">Faves</div>
                      </div>
                      <div className="text-center">
                        <div className="font-bold text-lg text-gray-800">{fan.skrivee}</div>
                        <div className="text-xs text-gray-500 font-semibold">Skrivee</div>
                      </div>
                    </div>

                    {/* Action Buttons */}
                    <div className="flex gap-2">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          // Handle follow action here
                          console.log('Follow clicked for:', fan.name);
                        }}
                        className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-semibold hover:bg-blue-700 transition-all duration-300 flex-1"
                      >
                        Follow
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          // Handle message action here
                          console.log('Message clicked for:', fan.name);
                        }}
                        className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm font-semibold hover:bg-gray-200 transition-all duration-300 flex-1"
                      >
                        Message
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate('/memberProfile');
                        }}
                        className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm font-semibold hover:bg-gray-200 transition-all duration-300 flex-1"
                      >
                        Profile
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Social Links - Only show when not in fans view */}
      {currentView === 'info' && (
        <div className="bg-white/95 backdrop-blur-lg rounded-3xl border border-gray-200/50 p-6 sm:p-8 lg:p-10 mt-8 sm:mt-10 mx-4 sm:mx-6 lg:mx-8 shadow-2xl hover:shadow-3xl transition-all duration-700 relative overflow-hidden group">
        {/* Enhanced Background Pattern */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/40 via-purple-50/20 to-pink-50/40 opacity-0 group-hover:opacity-100 transition-opacity duration-700"></div>
        <div className="absolute top-0 left-0 w-full h-1 bg-[#4A99F8]"></div>
        <div className="absolute -top-16 -right-16 w-32 h-32 bg-gradient-to-br from-purple-400/10 to-pink-400/10 rounded-full blur-2xl"></div>
        <div className="absolute -bottom-16 -left-16 w-32 h-32 bg-gradient-to-tr from-blue-400/10 to-purple-400/10 rounded-full blur-2xl"></div>

        <div className="relative z-10">
          <h3 className="text-lg sm:text-xl font-bold text-gray-800 mb-4 sm:mb-6 text-center">Connect With Me</h3>
          <div className="space-y-3 sm:space-y-4">
            {[
              {
                icon: <FaInstagram size={16} className="sm:w-5 sm:h-5" />,
                color: 'bg-gradient-to-br from-purple-500 to-pink-500',
                label: profileData.social.instagram,
                hoverColor: 'hover:from-purple-600 hover:to-pink-600'
              },
              {
                icon: <FaFacebook size={16} className="sm:w-5 sm:h-5" />,
                color: 'bg-blue-600',
                label: profileData.social.facebook,
                hoverColor: 'hover:bg-blue-700'
              },
              {
                icon: <FaLinkedin size={16} className="sm:w-5 sm:h-5" />,
                color: 'bg-blue-700',
                label: profileData.social.linkedin,
                hoverColor: 'hover:bg-blue-800'
              },
              {
                icon: <FaTwitter size={16} className="sm:w-5 sm:h-5" />,
                color: 'bg-blue-400',
                label: profileData.social.twitter,
                hoverColor: 'hover:bg-blue-500'
              },
              {
                icon: <SiGoodreads size={16} className="sm:w-5 sm:h-5" />,
                color: 'bg-amber-700',
                label: profileData.social.goodreads,
                hoverColor: 'hover:bg-amber-800'
              },
            ].map(({ icon, color, label, hoverColor }, idx) => (
              <div key={idx} className="group/item flex items-center gap-3 sm:gap-4 lg:gap-5 p-3 sm:p-4 hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50 rounded-lg sm:rounded-xl transition-all duration-300 cursor-pointer hover:shadow-lg transform hover:scale-105">
                <div className={`w-10 h-10 sm:w-12 sm:h-12 lg:w-14 lg:h-14 ${color} ${hoverColor} rounded-full flex items-center justify-center text-white shadow-lg group-hover/item:shadow-xl transition-all duration-300 group-hover/item:scale-110 group-hover/item:rotate-12`}>
                  {icon}
                </div>
                <span className="text-gray-700 text-sm sm:text-base font-semibold group-hover/item:text-[#4A99F8] transition-colors duration-300 truncate flex-1">{label}</span>
                <div className="ml-auto opacity-0 group-hover/item:opacity-100 transition-opacity duration-300 hidden sm:block">
                  <svg className="w-4 h-4 sm:w-5 sm:h-5 text-[#4A99F8]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </div>
              </div>
            ))}
          </div>
        </div>
        </div>
      )}

      </div>
    </>
  );
}

export default MyProfilePage;
