/* Author Ranking Page Styles */

/* Content Area */
.content-area {
  width: 100%;
  height: calc(100vh - 100px);
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  display: flex;
  flex-direction: column;
}

/* Tab Navigation */
.tab-navigation {
  display: flex;
  background: linear-gradient(135deg, #4A99F8 0%, #0A06F4 100%);
  padding: 0;
}

.tab-btn {
  flex: 1;
  padding: 16px 24px;
  background: transparent;
  border: none;
  color: white;
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s;
  position: relative;
}

.tab-btn.active {
  background: rgba(255, 255, 255, 0.2);
}

.tab-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* Ranking Table */
.ranking-table {
  padding: 0;
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.table-header {
  display: grid;
  grid-template-columns: 80px 1fr 120px 120px;
  background: linear-gradient(135deg, #40e0d0 0%, #36d1dc 50%, #5b86e5 100%);
  color: white;
  font-weight: 600;
  font-size: 16px;
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2px 8px rgba(64, 224, 208, 0.3);
}

.header-cell {
  padding: 16px 20px;
  text-align: left;
}

.table-body {
  background: white;
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0;
}

/* Custom Scrollbar */
.table-body::-webkit-scrollbar {
  width: 8px;
}

.table-body::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.table-body::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #40e0d0, #5b86e5);
  border-radius: 4px;
  transition: all 0.3s ease;
}

.table-body::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #36d1dc, #667eea);
}

.table-row {
  display: grid;
  grid-template-columns: 80px 1fr 120px 120px;
  border-bottom: 1px solid #f1f5f9;
  transition: all 0.3s ease;
  position: relative;
  animation: fadeInUp 0.5s ease forwards;
  opacity: 0;
  transform: translateY(20px);
}

.table-row:nth-child(1) { animation-delay: 0.1s; }
.table-row:nth-child(2) { animation-delay: 0.2s; }
.table-row:nth-child(3) { animation-delay: 0.3s; }
.table-row:nth-child(4) { animation-delay: 0.4s; }
.table-row:nth-child(5) { animation-delay: 0.5s; }
.table-row:nth-child(6) { animation-delay: 0.6s; }
.table-row:nth-child(7) { animation-delay: 0.7s; }

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.table-row:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  transform: translateX(5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-left: 4px solid #40e0d0;
}

.table-row:hover .rank-cell {
  color: #40e0d0;
  font-weight: 700;
  transform: scale(1.1);
}

.table-row:hover .author-avatar {
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(64, 224, 208, 0.3);
}

.cell {
  padding: 16px 20px;
  display: flex;
  align-items: center;
}

.rank-cell {
  font-weight: 600;
  font-size: 18px;
  color: #1e293b;
  transition: all 0.3s ease;
  position: relative;
}

/* No special styling for top 3 ranks - all ranks behave the same */

.name-cell {
  gap: 12px;
}

.author-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  position: relative;
}

.author-avatar::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background: linear-gradient(135deg, #40e0d0, #5b86e5);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.table-row:hover .author-avatar::before {
  opacity: 1;
}

.author-name {
  font-weight: 500;
  color: #1e293b;
  transition: all 0.3s ease;
}

.table-row:hover .author-name {
  color: #40e0d0;
  font-weight: 600;
}

.points-cell {
  font-weight: 600;
  color: #059669;
  font-size: 16px;
  transition: all 0.3s ease;
}

.table-row:hover .points-cell {
  color: #40e0d0;
  transform: scale(1.05);
}

.follow-btn {
  background: #4A99F8;
  color: white;
  border: none;
  padding: 8px 20px;
  border-radius: 20px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.follow-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s ease;
}

.follow-btn:hover::before {
  left: 100%;
}

.follow-btn:hover {
  background: #0A06F4;
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(74, 153, 248, 0.4);
}

/* Responsive Design */
@media (max-width: 768px) {
  .table-header, .table-row {
    grid-template-columns: 60px 1fr 80px 80px;
  }

  .header-cell, .cell {
    padding: 12px 8px;
    font-size: 14px;
  }
}
