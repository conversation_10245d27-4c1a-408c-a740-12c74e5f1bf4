import { useState } from 'react';
import './AuthorRanking.css';

const AuthorRanking = () => {
  const [activeTab, setActiveTab] = useState('authors');

  const authors = [
    {
      rank: 1,
      name: "<PERSON><PERSON><PERSON>",
      points: 1253,
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=face"
    },
    {
      rank: 2,
      name: "<PERSON><PERSON><PERSON>",
      points: 1250,
      avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=50&h=50&fit=crop&crop=face"
    },
    {
      rank: 3,
      name: "<PERSON><PERSON><PERSON>",
      points: 1234,
      avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=50&h=50&fit=crop&crop=face"
    },
    {
      rank: 4,
      name: "<PERSON><PERSON>",
      points: 1108,
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=50&h=50&fit=crop&crop=face"
    },
    {
      rank: 5,
      name: "Bhavna Goyal",
      points: 1000,
      avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=50&h=50&fit=crop&crop=face"
    },
    {
      rank: 6,
      name: "Nandini Kumar",
      points: 991,
      avatar: "https://images.unsplash.com/photo-1534528741775-53994a69daeb?w=50&h=50&fit=crop&crop=face"
    },
    {
      rank: 7,
      name: "Jeevan Das",
      points: 856,
      avatar: "https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=50&h=50&fit=crop&crop=face"
    }
  ];

  const publications = [
    {
      rank: 1,
      name: "The Art of Storytelling",
      points: 2150,
      avatar: "https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=50&h=50&fit=crop"
    },
    {
      rank: 2,
      name: "Modern Poetry Collection",
      points: 1980,
      avatar: "https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=50&h=50&fit=crop"
    },
    {
      rank: 3,
      name: "Digital Marketing Guide",
      points: 1875,
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop"
    },
    {
      rank: 4,
      name: "Science Fiction Tales",
      points: 1654,
      avatar: "https://images.unsplash.com/photo-1589998059171-988d887df646?w=50&h=50&fit=crop"
    },
    {
      rank: 5,
      name: "Cooking Masterclass",
      points: 1432,
      avatar: "https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=50&h=50&fit=crop"
    },
    {
      rank: 6,
      name: "Travel Adventures",
      points: 1298,
      avatar: "https://images.unsplash.com/photo-1544947950-fa07a98d237f?w=50&h=50&fit=crop"
    },
    {
      rank: 7,
      name: "Business Strategy",
      points: 1156,
      avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop"
    }
  ];

  const currentData = activeTab === 'authors' ? authors : publications;

  return (
    <div className="content-area">
      {/* Tab Navigation */}
      <div className="tab-navigation">
        <button
          className={`tab-btn ${activeTab === 'authors' ? 'active' : ''}`}
          onClick={() => setActiveTab('authors')}
        >
          Top Authors
        </button>
        <button
          className={`tab-btn ${activeTab === 'publications' ? 'active' : ''}`}
          onClick={() => setActiveTab('publications')}
        >
          Top Publication
        </button>
      </div>

      {/* Ranking Table */}
      <div className="ranking-table">
        <div className="table-header">
          <div className="header-cell rank-col">Rank</div>
          <div className="header-cell name-col">Name</div>
          <div className="header-cell points-col">Points</div>
          <div className="header-cell action-col"></div>
        </div>

        <div className="table-body">
          {currentData.map((item) => (
            <div key={item.rank} className="table-row">
              <div className="cell rank-cell">{item.rank}</div>
              <div className="cell name-cell">
                <img src={item.avatar} alt={item.name} className="author-avatar" />
                <span className="author-name">{item.name}</span>
              </div>
              <div className="cell points-cell">{item.points}</div>
              <div className="cell action-cell">
                <button className="follow-btn">Follow</button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AuthorRanking;
