@import "tailwindcss";

html {
  scrollbar-gutter: stable;
}


body{
  background-color:#F8F8F8;
  padding: 0;
  margin: 0;
}

/* AuthorPitch Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* image loading */
/* Base skeleton container */
.skeleton-box {
  position: relative;
  overflow: hidden;
  background-color: #f0f0f0;
  border-radius: 4px;
}

/* Add soft fade-in/out pulse behind shimmer */
.skeleton-box::before {
  content: "";
  position: absolute;
  inset: 0;
  background-color: #f0f0f0;
  animation: pulse-bg 2s ease-in-out infinite;
}

@keyframes pulse-bg {
  0%, 100% { opacity: 1; }
  50%      { opacity: 0.9; }
}

/* Shimmer overlays – two layers for richness */
.skeleton-box::after {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(
    90deg,
    rgba(255,255,255,0) 0%,
    rgba(255,255,255,0.6) 50%,
    rgba(255,255,255,0) 100%
  );
  transform: translateX(-100%);
  animation: shimmer 1.2s ease-in-out infinite;
}

.skeleton-box::before,
.skeleton-box::after {
  border-radius: inherit;
}

/* Stronger shimmer pulse layer */
.skeleton-box.shimmer-strong::after {
  animation-duration: 0.8s;
  opacity: 0.8;
}

@keyframes shimmer {
  0%   { transform: translateX(-100%); }
  100% { transform: translateX(100%);  }
}

/* universal colors */
.light-combo-bg-primary       { background-color: #4A99F8; }   /* // for buttons */
.light-combo-bg-primary-dark  { background-color: #0A06F4; }   /* // for button hover */
.light-combo-text-primary     { color: #4A99F8; }              /* // for links/icons */
.light-combo-text-primary-dark{ color: #0A06F4; }              /* // for emphasis */

/* --- Secondary --- */
.light-combo-bg-secondary     { background-color: #76D6FC; }   /* // for badges/info chips */
.light-combo-text-secondary   { color: #76D6FC; }              /* // for tag text */

/* --- Surface (Light Background) --- */
.light-combo-bg-surface       { background-color: #F8F9FA; }   /* // for full background */
.light-combo-text-surface     { color: #F8F9FA; }              /* // use carefully */

/* --- Accent Colors --- */
.light-combo-bg-accent-pink   { background-color: #D99FE9; }   /* // for badges/tags */
.light-combo-text-accent-pink { color: #D99FE9; }              /* // tag text or highlights */

.light-combo-bg-accent-purple { background-color: #CDAEEC; }   /* // for card/post bg */
.light-combo-text-accent-purple { color: #CDAEEC; }            /* // for post text if needed */

/* --- Info/Highlight --- */
.light-combo-bg-info          { background-color: #48CAE4; }   /* // info alerts/chips */
.light-combo-text-info        { color: #48CAE4; }              /* // text for info badges */

/* --- Utility Shades --- */
.light-combo-bg-muted         { background-color: #E6EAF0; }   /* // optional subtle bg */
.light-combo-text-muted       { color: #A5ACB8; }              /* // optional for subtitles */
