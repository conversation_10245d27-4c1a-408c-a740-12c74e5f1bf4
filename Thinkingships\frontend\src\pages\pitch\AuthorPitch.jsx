import { useState } from 'react';

const AuthorPitch = () => {
  const [activeTab, setActiveTab] = useState('Browse Request');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [filters, setFilters] = useState({
    contentType: 'All',
    genre: 'All',
    language: 'All',
    payout: 'All',
    deadline: 'All'
  });
  const [formData, setFormData] = useState({
    title: '',
    brief: '',
    contentType: '',
    genre: '',
    deadline: '',
    wordCount: '',
    language: '',
    tags: '',
    guidelines: '',
    requireFirstRights: false,
    showSubmissionCount: false,
    autoCloseDeadline: false
  });

  const browseRequests = [
    {
      id: 1,
      title: 'Voices From The City',
      publication: 'Inkspire Magazine',
      genre: 'Urban Life',
      type: 'Blog',
      payout: '₹500',
      status: 'Open',
      deadline: 'July 19, 2025',
      description: 'Share A Compelling Article About Life In An Indian Metro City — The Chaos, The Quiet Moments, The Everyday Characters, And The Ever-Changing Pace.',
      avatar: 'https://randomuser.me/api/portraits/women/44.jpg'
    },
    {
      id: 2,
      title: 'Echoes Of The Forgotten',
      publication: 'Inkspire Magazine',
      genre: 'Romance',
      type: 'Story',
      payout: '₹750',
      status: 'Open',
      deadline: 'July 19, 2025',
      description: 'A Haunting Narrative About People, Places, Or Memories That Time Has Left Behind. We\'re Looking For Stories That Blend Nostalgia With Quiet Revelation.',
      avatar: 'https://randomuser.me/api/portraits/men/32.jpg'
    },
    {
      id: 3,
      title: 'Midnight Chronicles',
      publication: 'Inkspire Magazine',
      genre: 'Fantasy',
      type: 'Story',
      payout: '₹600',
      status: 'Open',
      deadline: 'Jul 10, 2025',
      description: 'Create an epic fantasy tale with Indian mythology elements. Include magical realism and cultural authenticity.',
      avatar: 'https://randomuser.me/api/portraits/women/68.jpg'
    }
  ];

  const myPitches = [
    {
      id: 1,
      title: 'Voices From The City',
      publication: 'Inkspire Magazine',
      genre: 'Urban Life',
      type: 'Blog',
      status: 'Accepted',
      deadline: 'May 29, 2025',
      avatar: 'https://randomuser.me/api/portraits/women/44.jpg'
    },
    {
      id: 2,
      title: 'Voices From The City',
      publication: 'Inkspire Magazine',
      genre: 'Romance',
      type: 'Story',
      status: 'Pending',
      deadline: 'Jul 14, 2025',
      avatar: 'https://randomuser.me/api/portraits/men/32.jpg'
    },
    {
      id: 3,
      title: 'Voices From The City',
      publication: 'Inkspire Magazine',
      genre: 'Fantasy',
      type: 'Poem',
      status: 'Rejected',
      deadline: 'Mar 23, 2025',
      avatar: 'https://randomuser.me/api/portraits/women/68.jpg'
    },
    {
      id: 4,
      title: 'Voices From The City',
      publication: 'Inkspire Magazine',
      genre: 'Science Fiction',
      type: 'e-book',
      status: 'Pending',
      deadline: 'Dec 29, 2024',
      avatar: 'https://randomuser.me/api/portraits/men/45.jpg'
    }
  ];

  const handleFilterChange = (field, value) => {
    setFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCreateNew = () => {
    setShowCreateForm(true);
  };

  const handleFormChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCloseForm = () => {
    setShowCreateForm(false);
    setFormData({
      title: '',
      brief: '',
      contentType: '',
      genre: '',
      deadline: '',
      wordCount: '',
      language: '',
      tags: '',
      guidelines: '',
      requireFirstRights: false,
      showSubmissionCount: false,
      autoCloseDeadline: false
    });
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
    // Handle form submission here
    handleCloseForm();
  };

  const currentData = activeTab === 'Browse Request' ? browseRequests : myPitches;

  const filteredPitches = currentData.filter(pitch => {
    if (filters.contentType !== 'All' && pitch.type !== filters.contentType) return false;
    if (filters.genre !== 'All' && pitch.genre !== filters.genre) return false;
    if (filters.language !== 'All' && pitch.language !== filters.language) return false;
    if (filters.payout !== 'All' && pitch.payout !== filters.payout) return false;
    if (filters.deadline !== 'All' && pitch.deadline !== filters.deadline) return false;
    return true;
  });

  return (
    <div className="p-6 bg-gradient-to-br from-slate-50 to-slate-200 min-h-screen relative overflow-y-auto overflow-x-hidden opacity-0" style={{ animation: 'fadeInUp 0.6s ease-out forwards' }}>
      {/* Header, Tabs and Filters - Hide when create form is shown */}
      {!showCreateForm && (
        <>
          {/* Header */}
          <div className="mb-8 relative z-10 opacity-0 -translate-y-4" style={{ animation: 'slideInUp 0.6s ease-out 0.1s forwards' }}>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-500 to-blue-700 bg-clip-text text-transparent relative">
              Pitch Hub
              <div className="absolute -bottom-2 left-0 w-15 h-1 bg-gradient-to-r from-blue-500 to-blue-700 rounded-full"></div>
            </h1>
          </div>

          {/* Tabs */}
          <div className="flex gap-2 mb-6 opacity-0 -translate-x-4" style={{ animation: 'slideInUp 0.6s ease-out 0.2s forwards' }}>
            <button
              className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                activeTab === 'Browse Request'
                  ? 'bg-blue-500 text-white shadow-lg transform -translate-y-1'
                  : 'bg-white/70 text-gray-600 hover:bg-white/90 hover:text-gray-800'
              }`}
              onClick={() => setActiveTab('Browse Request')}
            >
              Browse Request
            </button>
            <button
              className={`px-6 py-3 rounded-xl font-semibold transition-all duration-300 ${
                activeTab === 'My Pitches'
                  ? 'bg-blue-500 text-white shadow-lg transform -translate-y-1'
                  : 'bg-white/70 text-gray-600 hover:bg-white/90 hover:text-gray-800'
              }`}
              onClick={() => setActiveTab('My Pitches')}
            >
              My Pitches
            </button>
          </div>

          {/* Subtitle - Only show for My Pitches tab */}
          {activeTab === 'My Pitches' && (
            <p className="text-gray-600 mb-6 opacity-0 -translate-x-4" style={{ animation: 'slideInUp 0.6s ease-out 0.3s forwards' }}>
              Track All Your Submitted Pitches To Publications And Businesses
            </p>
          )}

          {/* Filters */}
          <div className="flex gap-3 mb-8 opacity-0 translate-x-4" style={{ animation: 'slideInUp 0.6s ease-out 0.4s forwards' }}>
            <select
              value={filters.contentType}
              onChange={(e) => handleFilterChange('contentType', e.target.value)}
              className="px-3 py-2 bg-white/80 backdrop-blur-sm border border-blue-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 text-sm min-w-28"
            >
              <option value="All">Content Type</option>
              <option value="Blog">Blog</option>
              <option value="Story">Story</option>
              <option value="Article">Article</option>
            </select>

            <select
              value={filters.genre}
              onChange={(e) => handleFilterChange('genre', e.target.value)}
              className="px-3 py-2 bg-white/80 backdrop-blur-sm border border-blue-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 text-sm min-w-28"
            >
              <option value="All">Genre</option>
              <option value="Romance">Romance</option>
              <option value="Fantasy">Fantasy</option>
              <option value="Urban Life">Urban Life</option>
              <option value="Science Fiction">Science Fiction</option>
              <option value="Environmental">Environmental</option>
            </select>

            <select
              value={filters.language}
              onChange={(e) => handleFilterChange('language', e.target.value)}
              className="px-3 py-2 bg-white/80 backdrop-blur-sm border border-blue-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 text-sm min-w-28"
            >
              <option value="All">Language</option>
              <option value="English">English</option>
              <option value="Hindi">Hindi</option>
              <option value="Bengali">Bengali</option>
            </select>

            <select
              value={filters.payout}
              onChange={(e) => handleFilterChange('payout', e.target.value)}
              className="px-3 py-2 bg-white/80 backdrop-blur-sm border border-blue-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 text-sm min-w-28"
            >
              <option value="All">Payout</option>
              <option value="₹400">₹400</option>
              <option value="₹500">₹500</option>
              <option value="₹600">₹600</option>
              <option value="₹750">₹750</option>
            </select>

            <select
              value={filters.deadline}
              onChange={(e) => handleFilterChange('deadline', e.target.value)}
              className="px-3 py-2 bg-white/80 backdrop-blur-sm border border-blue-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 text-sm min-w-28"
            >
              <option value="All">Deadline</option>
              <option value="May 29, 2025">May 29, 2025</option>
              <option value="Jun 15, 2025">Jun 15, 2025</option>
              <option value="Jul 10, 2025">Jul 10, 2025</option>
              <option value="Jul 14, 2025">Jul 14, 2025</option>
              <option value="Mar 23, 2025">Mar 23, 2025</option>
              <option value="Dec 29, 2024">Dec 29, 2024</option>
            </select>
          </div>
        </>
      )}

      {/* Pitch List */}
      {!showCreateForm && (
        <div className="space-y-6">
          {activeTab === 'Browse Request' ? (
            // Card format for Browse Request (same as Pitch page)
            filteredPitches.map((pitch, index) => (
              <div
                key={pitch.id}
                className="bg-white/95 backdrop-blur-xl rounded-2xl p-6 border border-blue-100 shadow-blue-100 hover:border-blue-200 hover:shadow-blue-200 transition-all duration-300 relative overflow-hidden opacity-0 translate-y-8 hover:scale-105 group"
                style={{
                  animationDelay: `${index * 0.1}s`,
                  animation: 'slideInUp 0.6s ease-out forwards',
                  boxShadow: '0 8px 32px rgba(59, 130, 246, 0.08)'
                }}
              >
                <div className="absolute inset-0 bg-gradient-to-r from-transparent via-blue-500/5 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-800"></div>
                <div className="flex justify-between items-start mb-5 relative z-10">
                  <div className="flex-1">
                    <h3 className="text-xl font-semibold text-gray-800 mb-3 leading-tight">{pitch.title}</h3>
                    <div className="flex items-center gap-3">
                      <img
                        src={pitch.avatar}
                        alt={pitch.publication}
                        className="w-10 h-10 rounded-full border-2 border-blue-200 transition-all duration-300 hover:scale-110 hover:border-blue-500"
                      />
                      <span className="font-semibold text-gray-700">{pitch.publication}</span>
                    </div>
                  </div>
                </div>

                <div className="mb-5 relative z-10">
                  <div className="flex gap-8 mb-3">
                    <div className="flex gap-2">
                      <span className="font-semibold text-gray-600 min-w-20">Genre:</span>
                      <span className="text-gray-800 font-medium">{pitch.genre}</span>
                    </div>
                    <div className="flex gap-2">
                      <span className="font-semibold text-gray-600 min-w-20">Type:</span>
                      <span className="text-gray-800 font-medium">{pitch.type}</span>
                    </div>
                  </div>
                  <div className="flex gap-8">
                    <div className="flex gap-2">
                      <span className="font-semibold text-gray-600 min-w-20">Payout:</span>
                      <span className="text-gray-800 font-medium">{pitch.payout}</span>
                    </div>
                    <div className="flex gap-2">
                      <span className="font-semibold text-gray-600 min-w-20">Deadline:</span>
                      <span className="text-gray-800 font-medium">{pitch.deadline}</span>
                    </div>
                  </div>
                </div>

                <div className="mb-6 relative z-10">
                  <p className="text-gray-700 leading-relaxed text-sm">{pitch.description}</p>
                </div>

                <div className="flex justify-end relative z-10">
                  <button className="bg-gradient-to-r from-blue-500 to-blue-700 text-white px-5 py-2.5 rounded-lg font-semibold transition-all duration-300 hover:-translate-y-0.5 hover:shadow-lg hover:shadow-blue-500/30 relative overflow-hidden group">
                    <span className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-500"></span>
                    View Details
                  </button>
                </div>
              </div>
            ))
          ) : (
            // Table format for My Pitches
            <div className="bg-white/95 backdrop-blur-xl rounded-2xl border border-blue-200 shadow-blue-100 overflow-hidden opacity-0 translate-y-4" style={{ animation: 'fadeInUp 0.6s ease-out forwards' }}>
              {/* Table Header */}
              <div className="bg-gradient-to-r from-blue-50 to-blue-100 px-6 py-4 border-b border-blue-200">
                <div className="grid grid-cols-12 gap-4 text-sm font-semibold text-gray-700">
                  <div className="col-span-4">Publication</div>
                  <div className="col-span-2">Type</div>
                  <div className="col-span-2">Status</div>
                  <div className="col-span-2">Deadline</div>
                  <div className="col-span-2">Action</div>
                </div>
              </div>

              {/* Table Body */}
              <div className="divide-y divide-gray-100">
                {filteredPitches.map((pitch, index) => (
                  <div
                    key={pitch.id}
                    className="px-6 py-4 hover:bg-blue-50/50 transition-all duration-300 opacity-0 translate-y-4"
                    style={{
                      animationDelay: `${index * 0.1}s`,
                      animation: 'slideInUp 0.6s ease-out forwards'
                    }}
                  >
                    <div className="grid grid-cols-12 gap-4 items-center">
                      {/* Publication */}
                      <div className="col-span-4 flex items-center gap-3">
                        <div className="w-12 h-12 rounded-full overflow-hidden border-2 border-blue-200 flex-shrink-0">
                          <img
                            src={pitch.avatar}
                            alt="Publication"
                            className="w-full h-full object-cover"
                          />
                        </div>
                        <div>
                          <h3 className="font-semibold text-gray-800">{pitch.title}</h3>
                          <p className="text-sm text-blue-600">{pitch.publication}</p>
                        </div>
                      </div>

                      {/* Type */}
                      <div className="col-span-2">
                        <span className="text-gray-700">{pitch.type}</span>
                      </div>

                      {/* Status */}
                      <div className="col-span-2">
                        <span className={`px-3 py-1 rounded-full text-sm font-semibold ${
                          pitch.status === 'Accepted'
                            ? 'bg-green-100 text-green-700'
                            : pitch.status === 'Pending'
                            ? 'bg-yellow-100 text-yellow-700'
                            : pitch.status === 'Rejected'
                            ? 'bg-red-100 text-red-700'
                            : 'bg-blue-100 text-blue-700'
                        }`}>
                          {pitch.status}
                        </span>
                      </div>

                      {/* Deadline */}
                      <div className="col-span-2">
                        <span className="text-gray-700">{pitch.deadline}</span>
                      </div>

                      {/* Action */}
                      <div className="col-span-2">
                        <button className="px-4 py-2 bg-blue-500 text-white rounded-lg font-semibold hover:bg-blue-600 transition-all duration-300 transform hover:-translate-y-0.5">
                          Action
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Create Form - Hidden by default since Create New button is removed */}
      {showCreateForm && (
        <div className="animate-slideInUp">
          <div className="max-w-4xl mx-auto bg-white/95 backdrop-blur-xl rounded-2xl shadow-2xl border border-blue-200 overflow-hidden">
            <div className="flex justify-between items-center p-6 border-b border-blue-100 bg-gradient-to-r from-blue-50 to-indigo-50">
              <h2 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">Create Submission Guidelines</h2>
              <button className="p-2 hover:bg-blue-100 rounded-lg transition-colors duration-200" onClick={handleCloseForm}>
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" className="text-slate-600">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </div>

            <div className="p-6 text-slate-600 bg-blue-50/50">
              Set up your submission requirements to invite Authors to share their best work. Define what you're looking for and the qualifications Authors need to meet.
            </div>

            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              {/* Title and Brief */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-2">Title</label>
                  <div className="text-sm text-slate-500 mb-2">Give your submission guidelines a clear, descriptive title</div>
                  <input
                    type="text"
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                    placeholder="e.g., 'Short Stories for Urban Life Magazine'"
                    value={formData.title}
                    onChange={(e) => handleFormChange('title', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-2">Brief</label>
                  <div className="text-sm text-slate-500 mb-2">Provide a short summary of what you're looking for</div>
                  <input
                    type="text"
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                    placeholder="Brief description of your requirements"
                    value={formData.brief}
                    onChange={(e) => handleFormChange('brief', e.target.value)}
                    required
                  />
                </div>
              </div>

              {/* Content Type and Genre */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-2">Content Type</label>
                  <select
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                    value={formData.contentType}
                    onChange={(e) => handleFormChange('contentType', e.target.value)}
                    required
                  >
                    <option value="">Select content type</option>
                    <option value="Blog">Blog</option>
                    <option value="Story">Story</option>
                    <option value="Article">Article</option>
                    <option value="Poetry">Poetry</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-2">Genre</label>
                  <select
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                    value={formData.genre}
                    onChange={(e) => handleFormChange('genre', e.target.value)}
                    required
                  >
                    <option value="">Select genre</option>
                    <option value="Romance">Romance</option>
                    <option value="Fantasy">Fantasy</option>
                    <option value="Mystery">Mystery</option>
                    <option value="Science Fiction">Science Fiction</option>
                    <option value="Horror">Horror</option>
                    <option value="Travel">Travel</option>
                    <option value="Food">Food</option>
                    <option value="Technology">Technology</option>
                  </select>
                </div>
              </div>

              {/* Deadline and Word Count */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-2">Deadline</label>
                  <input
                    type="date"
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                    value={formData.deadline}
                    onChange={(e) => handleFormChange('deadline', e.target.value)}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-2">Word Count</label>
                  <input
                    type="text"
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                    placeholder="e.g., 500-1000 words"
                    value={formData.wordCount}
                    onChange={(e) => handleFormChange('wordCount', e.target.value)}
                    required
                  />
                </div>
              </div>

              {/* Language and Tags */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-2">Language</label>
                  <select
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                    value={formData.language}
                    onChange={(e) => handleFormChange('language', e.target.value)}
                    required
                  >
                    <option value="">Select language</option>
                    <option value="English">English</option>
                    <option value="Hindi">Hindi</option>
                    <option value="Spanish">Spanish</option>
                    <option value="French">French</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-2">Tags</label>
                  <input
                    type="text"
                    className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                    placeholder="Add relevant tags (comma separated)"
                    value={formData.tags}
                    onChange={(e) => handleFormChange('tags', e.target.value)}
                  />
                </div>
              </div>

              {/* Submission Guidelines */}
              <div>
                <label className="block text-sm font-semibold text-slate-700 mb-2">Submission Guidelines</label>
                <div className="text-sm text-slate-500 mb-2">Describe the specific qualities, themes, or styles you expect in submitted drafts</div>
                <textarea
                  className="w-full px-4 py-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                  placeholder="Outline what you're looking for in a submission — tone, themes, structure, or any dos and don'ts."
                  value={formData.guidelines}
                  onChange={(e) => handleFormChange('guidelines', e.target.value)}
                  rows="4"
                  required
                />
                <div className="mt-3 p-3 bg-blue-50 border-l-4 border-blue-500 rounded-lg">
                  <em className="text-sm text-slate-600">
                    Tip: Clearly define your content requirements, themes or guidelines to help authors submit work that aligns with your publication's needs.
                  </em>
                </div>
              </div>

              {/* Checkbox Options */}
              <div>
                <div className="space-y-4">
                  <label className="flex items-center gap-3 p-4 bg-blue-50/50 border border-blue-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-all duration-300 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.requireFirstRights}
                      onChange={(e) => handleFormChange('requireFirstRights', e.target.checked)}
                      className="w-5 h-5 text-blue-600 rounded focus:ring-blue-500"
                    />
                    <span className="font-medium text-slate-700">Require first Publication Rights</span>
                  </label>

                  <label className="flex items-center gap-3 p-4 bg-blue-50/50 border border-blue-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-all duration-300 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.showSubmissionCount}
                      onChange={(e) => handleFormChange('showSubmissionCount', e.target.checked)}
                      className="w-5 h-5 text-blue-600 rounded focus:ring-blue-500"
                    />
                    <span className="font-medium text-slate-700">Show number of Submissions</span>
                  </label>

                  <label className="flex items-center gap-3 p-4 bg-blue-50/50 border border-blue-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-all duration-300 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.autoCloseDeadline}
                      onChange={(e) => handleFormChange('autoCloseDeadline', e.target.checked)}
                      className="w-5 h-5 text-blue-600 rounded focus:ring-blue-500"
                    />
                    <span className="font-medium text-slate-700">Auto-Close after Deadline</span>
                  </label>
                </div>
              </div>

              {/* Make Guidelines Live Section */}
              <div className="mt-8 p-6 bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-2xl text-center">
                <h3 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-3">
                  Make Your Guidelines Live
                </h3>
                <p className="text-slate-600 leading-relaxed">
                  Everything's ready! Publish your submission guidelines to start receiving high-quality drafts from talented writers.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center pt-6 border-t border-blue-100">
                <button
                  type="button"
                  className="px-6 py-3 border-2 border-gray-400/30 bg-transparent text-gray-500 rounded-lg font-semibold hover:border-gray-500 hover:text-gray-700 transform hover:-translate-y-1 transition-all duration-300"
                >
                  Save As Draft
                </button>
                <button
                  type="button"
                  className="px-6 py-3 border-2 border-blue-500 bg-transparent text-blue-500 rounded-lg font-semibold hover:bg-blue-50 transform hover:-translate-y-1 transition-all duration-300"
                >
                  Preview
                </button>
                <button
                  type="submit"
                  className="px-8 py-3.5 bg-gradient-to-r from-blue-500 to-blue-700 text-white border-none rounded-xl font-semibold cursor-pointer transition-all duration-300 shadow-lg hover:transform hover:-translate-y-1 hover:shadow-xl relative overflow-hidden shimmer-effect"
                >
                  <span className="relative z-10">Set Live</span>
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
};

export default AuthorPitch;
