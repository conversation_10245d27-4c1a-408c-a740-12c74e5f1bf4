import React, { useState } from 'react';
import './Pitch.css';

const CreateSubmissionGuidelines = ({ open, onClose }) => {
  if (!open) return null;

  return (
    <div className="modal-overlay">
      <div className="modal-content submission-guidelines-modal">
        <button className="close-modal-btn" onClick={onClose}>&times;</button>
        <h2 className="submission-title">Create Submission Guidelines</h2>
        <p className="submission-desc">
          Set up your submission requirements to invite Authors to share their best work. Define what you’re looking for and the qualifications Authors need to meet.
        </p>
        <form className="submission-form">
          <div className="form-row">
            <label>Title:</label>
            <input type="text" placeholder="Enter title" />
          </div>
          <div className="form-row">
            <label>Brief:</label>
            <input type="text" placeholder="Enter brief description" />
          </div>
          <div className="form-row form-row-2col">
            <div>
              <label>Content Type</label>
              <select><option>select</option></select>
            </div>
            <div>
              <label>Genre</label>
              <select><option>select</option></select>
            </div>
          </div>
          <div className="form-row form-row-2col">
            <div>
              <label>Deadline</label>
              <input type="date" />
            </div>
            <div>
              <label>Word Count</label>
              <input type="number" placeholder="Enter" />
            </div>
          </div>
          <div className="form-row form-row-2col">
            <div>
              <label>Language</label>
              <select><option>select</option></select>
            </div>
            <div>
              <label>Tags</label>
              <input type="text" placeholder="Enter" />
            </div>
          </div>
          <div className="form-row">
            <label>Submission Guidelines</label>
            <textarea placeholder="Outline what you're looking for in a submission — tone, themes, structure, or any dos and don'ts."></textarea>
          </div>
          <button type="submit" className="submit-btn">Create</button>
        </form>
      </div>
    </div>
  );
};

export default CreateSubmissionGuidelines; 